package com.tech.ekvayu

import android.content.Intent
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import java.io.File

class PdfTestActivity : AppCompatActivity() {

    private lateinit var pdfListView: ListView
    private lateinit var refreshButton: Button
    private lateinit var statusText: TextView
    
    private val pdfFiles = mutableListOf<File>()
    private lateinit var adapter: ArrayAdapter<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(createLayout())
        
        setupViews()
        loadPdfFiles()
    }

    private fun createLayout(): LinearLayout {
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // Title
        val titleText = TextView(this).apply {
            text = "📄 PDF Files - Email Downloads"
            textSize = 20f
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 16)
        }

        // Status
        statusText = TextView(this).apply {
            text = "Loading PDF files..."
            textSize = 14f
            setPadding(0, 0, 0, 8)
        }

        // Refresh button
        refreshButton = Button(this).apply {
            text = "🔄 Refresh List"
            setOnClickListener { loadPdfFiles() }
        }

        // PDF list
        pdfListView = ListView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f // Take remaining space
            )
        }

        // Instructions
        val instructionsText = TextView(this).apply {
            text = """
                📧 Instructions:
                1. Single-click on any email in Gmail
                2. PDF files will be created automatically
                3. Tap on any PDF below to view it
                4. Use the PDF reader to verify content
            """.trimIndent()
            textSize = 12f
            setPadding(0, 16, 0, 0)
        }

        mainLayout.addView(titleText)
        mainLayout.addView(statusText)
        mainLayout.addView(refreshButton)
        mainLayout.addView(pdfListView)
        mainLayout.addView(instructionsText)

        return mainLayout
    }

    private fun setupViews() {
        title = "PDF Test Viewer"
        
        adapter = ArrayAdapter(this, android.R.layout.simple_list_item_1, mutableListOf<String>())
        pdfListView.adapter = adapter
        
        pdfListView.setOnItemClickListener { _, _, position, _ ->
            if (position < pdfFiles.size) {
                showPdfOptions(pdfFiles[position])
            }
        }
    }

    private fun loadPdfFiles() {
        try {
            pdfFiles.clear()
            
            // Get Downloads directory
            val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            
            if (!downloadsDir.exists()) {
                statusText.text = "❌ Downloads directory not found"
                return
            }

            // Find all PDF files that start with "Email_"
            val allFiles = downloadsDir.listFiles() ?: arrayOf()
            val emailPdfs = allFiles.filter { file ->
                file.isFile && 
                file.name.lowercase().endsWith(".pdf") && 
                (file.name.startsWith("Email_") || file.name.startsWith("Email_Print_"))
            }.sortedByDescending { it.lastModified() } // Most recent first

            pdfFiles.addAll(emailPdfs)
            
            // Update adapter
            val displayNames = pdfFiles.map { file ->
                val size = formatFileSize(file.length())
                val date = java.text.SimpleDateFormat("MMM dd, HH:mm", java.util.Locale.US)
                    .format(java.util.Date(file.lastModified()))
                "${file.name}\n📁 $size • 📅 $date"
            }
            
            adapter.clear()
            adapter.addAll(displayNames)
            adapter.notifyDataSetChanged()
            
            // Update status
            statusText.text = "📄 Found ${pdfFiles.size} email PDF files"
            
            Log.d("PDFTest", "📄 Loaded ${pdfFiles.size} PDF files from Downloads")
            
        } catch (e: Exception) {
            Log.e("PDFTest", "❌ Error loading PDF files: ${e.message}")
            statusText.text = "❌ Error loading files: ${e.message}"
        }
    }

    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "$bytes B"
            bytes < 1024 * 1024 -> "${bytes / 1024} KB"
            else -> "${bytes / (1024 * 1024)} MB"
        }
    }

    private fun showPdfOptions(file: File) {
        val options = arrayOf("📖 View PDF", "🔍 Analyze Content")

        android.app.AlertDialog.Builder(this)
            .setTitle("📄 ${file.name}")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> openPdfFile(file)
                    1 -> analyzePdfContent(file)
                }
            }
            .show()
    }

    private fun openPdfFile(file: File) {
        try {
            Log.d("PDFTest", "📄 Opening PDF: ${file.name}")

            val intent = Intent(this, PdfReaderActivity::class.java)
            intent.putExtra("PDF_PATH", file.absolutePath)
            startActivity(intent)

        } catch (e: Exception) {
            Log.e("PDFTest", "❌ Error opening PDF: ${e.message}")
            Toast.makeText(this, "Error opening PDF: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun analyzePdfContent(file: File) {
        try {
            Log.d("PDFTest", "🔍 Analyzing PDF: ${file.name}")

            val intent = Intent(this, PdfAnalysisActivity::class.java)
            intent.putExtra("PDF_PATH", file.absolutePath)
            startActivity(intent)

        } catch (e: Exception) {
            Log.e("PDFTest", "❌ Error analyzing PDF: ${e.message}")
            Toast.makeText(this, "Error analyzing PDF: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    override fun onResume() {
        super.onResume()
        // Refresh the list when returning to this activity
        loadPdfFiles()
    }
}
