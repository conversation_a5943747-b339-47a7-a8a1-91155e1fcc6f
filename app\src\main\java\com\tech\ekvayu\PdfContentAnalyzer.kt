package com.tech.ekvayu

import android.content.Context
import android.graphics.Bitmap
import android.graphics.pdf.PdfRenderer
import android.os.ParcelFileDescriptor
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.IOException

class PdfContentAnalyzer {
    
    companion object {
        private const val TAG = "PDFAnalyzer"
        
        fun analyzePdfContent(context: Context, pdfPath: String): PdfAnalysisResult {
            val result = PdfAnalysisResult()
            
            try {
                val file = File(pdfPath)
                if (!file.exists()) {
                    result.error = "PDF file not found: $pdfPath"
                    return result
                }
                
                result.fileName = file.name
                result.fileSize = formatFileSize(file.length())
                result.filePath = pdfPath
                result.lastModified = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.US)
                    .format(java.util.Date(file.lastModified()))
                
                // Try to extract text content from PDF
                val textContent = extractTextFromPdf(file)
                result.extractedText = textContent
                
                // Analyze PDF structure using PdfRenderer
                val parcelFileDescriptor = ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY)
                val pdfRenderer = PdfRenderer(parcelFileDescriptor)
                
                result.pageCount = pdfRenderer.pageCount
                result.isValid = true
                
                // Get first page dimensions
                if (pdfRenderer.pageCount > 0) {
                    val firstPage = pdfRenderer.openPage(0)
                    result.pageWidth = firstPage.width
                    result.pageHeight = firstPage.height
                    firstPage.close()
                }
                
                pdfRenderer.close()
                parcelFileDescriptor.close()
                
                // Analyze content structure
                analyzeEmailContent(result)
                
                Log.d(TAG, "✅ PDF analysis completed: ${result.fileName}")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error analyzing PDF: ${e.message}", e)
                result.error = "Error analyzing PDF: ${e.message}"
            }
            
            return result
        }
        
        private fun extractTextFromPdf(file: File): String {
            return try {
                // Since we created the PDF, we know it contains email data
                // Let's try to read the original EML file if it exists
                val emlFileName = file.name.replace("Email_Print_", "Email_").replace(".pdf", ".eml")
                val emlFile = File(file.parent, emlFileName)
                
                if (emlFile.exists()) {
                    Log.d(TAG, "📧 Found corresponding EML file: ${emlFile.name}")
                    emlFile.readText()
                } else {
                    "PDF content extraction not available. Please use PDF reader to view content."
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error extracting text: ${e.message}")
                "Text extraction failed: ${e.message}"
            }
        }
        
        private fun analyzeEmailContent(result: PdfAnalysisResult) {
            val text = result.extractedText
            
            // Extract email components
            result.emailData = EmailDataExtracted().apply {
                // Extract From field
                from = extractField(text, "From:", "To:")
                
                // Extract To field  
                to = extractField(text, "To:", "CC:")
                    ?: extractField(text, "To:", "Subject:")
                    ?: extractField(text, "To:", "Date:")
                
                // Extract CC field
                cc = extractField(text, "CC:", "BCC:")
                    ?: extractField(text, "CC:", "Subject:")
                
                // Extract BCC field
                bcc = extractField(text, "BCC:", "Subject:")
                
                // Extract Subject
                subject = extractField(text, "Subject:", "Date:")
                    ?: extractField(text, "Subject:", "Message Body:")
                
                // Extract Date
                date = extractField(text, "Date:", "Message Body:")
                    ?: extractField(text, "Date:", "From:")
                
                // Extract Message Body
                messageBody = extractField(text, "Message Body:", "Attachments:")
                    ?: extractField(text, "Message Body:", null)
                
                // Extract Attachments
                attachments = extractField(text, "Attachments:", null)
            }
        }
        
        private fun extractField(text: String, startMarker: String, endMarker: String?): String? {
            return try {
                val startIndex = text.indexOf(startMarker)
                if (startIndex == -1) return null
                
                val contentStart = startIndex + startMarker.length
                val endIndex = if (endMarker != null) {
                    val end = text.indexOf(endMarker, contentStart)
                    if (end == -1) text.length else end
                } else {
                    text.length
                }
                
                text.substring(contentStart, endIndex).trim()
            } catch (e: Exception) {
                null
            }
        }
        
        private fun formatFileSize(bytes: Long): String {
            return when {
                bytes < 1024 -> "$bytes B"
                bytes < 1024 * 1024 -> "${bytes / 1024} KB"
                else -> "${bytes / (1024 * 1024)} MB"
            }
        }
    }
}

data class PdfAnalysisResult(
    var fileName: String = "",
    var fileSize: String = "",
    var filePath: String = "",
    var lastModified: String = "",
    var pageCount: Int = 0,
    var pageWidth: Int = 0,
    var pageHeight: Int = 0,
    var isValid: Boolean = false,
    var extractedText: String = "",
    var emailData: EmailDataExtracted? = null,
    var error: String? = null
)

data class EmailDataExtracted(
    var from: String? = null,
    var to: String? = null,
    var cc: String? = null,
    var bcc: String? = null,
    var subject: String? = null,
    var date: String? = null,
    var messageBody: String? = null,
    var attachments: String? = null
)
