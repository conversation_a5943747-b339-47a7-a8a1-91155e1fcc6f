package com.tech.ekvayu

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import java.io.File

class QuickPdfOpenerActivity : AppCompatActivity() {

    private lateinit var filePathInput: EditText
    private lateinit var openButton: Button
    private lateinit var analyzeButton: Button
    private lateinit var statusText: TextView
    private lateinit var quickButtons: LinearLayout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(createLayout())
        
        title = "📄 Quick PDF Opener"
        
        // Pre-fill with the specific file you mentioned
        val specificFile = "Email_Print_Fwd_backend_Code_Inbox_20250704_152249.pdf"
        val fullPath = "/storage/emulated/0/Download/$specificFile"
        filePathInput.setText(fullPath)
        
        checkFileExists(fullPath)
    }

    private fun createLayout(): LinearLayout {
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // Title
        val titleText = TextView(this).apply {
            text = "📄 Quick PDF Opener"
            textSize = 20f
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 16)
        }

        // File path input
        val pathLabel = TextView(this).apply {
            text = "📁 PDF File Path:"
            textSize = 14f
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 8)
        }

        filePathInput = EditText(this).apply {
            hint = "/storage/emulated/0/Download/your_file.pdf"
            textSize = 12f
            setPadding(8, 8, 8, 8)
            setBackgroundColor(android.graphics.Color.parseColor("#F5F5F5"))
        }

        // Status text
        statusText = TextView(this).apply {
            text = "Enter PDF file path above"
            textSize = 14f
            setPadding(0, 8, 0, 16)
        }

        // Quick access buttons
        val quickLabel = TextView(this).apply {
            text = "🚀 Quick Access:"
            textSize = 14f
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 8)
        }

        quickButtons = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 0, 0, 16)
        }

        // Add quick buttons for recent files
        addQuickButton("📧 Your Specific File", "Email_Print_Fwd_backend_Code_Inbox_20250704_152249.pdf")
        addQuickButton("📧 Cloudinary Newsletter", "Email_Print_Fwd_Newsletter_Cloudinary_Developer_Bytes_Inbox_20250704_150513.pdf")
        
        // Action buttons
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            setPadding(0, 16, 0, 0)
        }

        openButton = Button(this).apply {
            text = "📖 Open in PDF Reader"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            setOnClickListener { openPdfFile() }
        }

        analyzeButton = Button(this).apply {
            text = "🔍 Analyze Content"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            setOnClickListener { analyzePdfFile() }
        }

        buttonLayout.addView(openButton)
        buttonLayout.addView(analyzeButton)

        // Check file button
        val checkButton = Button(this).apply {
            text = "✅ Check File Exists"
            setOnClickListener { checkFileExists(filePathInput.text.toString()) }
        }

        mainLayout.addView(titleText)
        mainLayout.addView(pathLabel)
        mainLayout.addView(filePathInput)
        mainLayout.addView(statusText)
        mainLayout.addView(quickLabel)
        mainLayout.addView(quickButtons)
        mainLayout.addView(buttonLayout)
        mainLayout.addView(checkButton)

        return mainLayout
    }

    private fun addQuickButton(label: String, filename: String) {
        val button = Button(this).apply {
            text = label
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply { setMargins(0, 4, 0, 4) }
            
            setOnClickListener {
                val fullPath = "/storage/emulated/0/Download/$filename"
                filePathInput.setText(fullPath)
                checkFileExists(fullPath)
            }
        }
        quickButtons.addView(button)
    }

    private fun checkFileExists(filePath: String) {
        try {
            val file = File(filePath)
            if (file.exists()) {
                val sizeKB = file.length() / 1024
                val lastModified = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.US)
                    .format(java.util.Date(file.lastModified()))
                
                statusText.text = "✅ File found! Size: ${sizeKB}KB, Modified: $lastModified"
                statusText.setTextColor(android.graphics.Color.parseColor("#4CAF50"))
                
                openButton.isEnabled = true
                analyzeButton.isEnabled = true
                
                Log.d("QuickPDF", "✅ File exists: $filePath (${file.length()} bytes)")
            } else {
                statusText.text = "❌ File not found: ${file.name}"
                statusText.setTextColor(android.graphics.Color.parseColor("#F44336"))
                
                openButton.isEnabled = false
                analyzeButton.isEnabled = false
                
                Log.e("QuickPDF", "❌ File not found: $filePath")
                
                // Suggest alternative locations
                suggestAlternativeLocations(file.name)
            }
        } catch (e: Exception) {
            statusText.text = "❌ Error checking file: ${e.message}"
            statusText.setTextColor(android.graphics.Color.parseColor("#F44336"))
            Log.e("QuickPDF", "❌ Error checking file: ${e.message}")
        }
    }

    private fun suggestAlternativeLocations(filename: String) {
        val possiblePaths = listOf(
            "/storage/emulated/0/Download/$filename",
            "/storage/emulated/0/Documents/$filename",
            "/storage/emulated/0/Android/data/com.tech.ekvayu/files/$filename",
            "/data/data/com.tech.ekvayu/files/$filename"
        )

        for (path in possiblePaths) {
            val file = File(path)
            if (file.exists()) {
                statusText.text = "💡 Found at alternative location: $path"
                statusText.setTextColor(android.graphics.Color.parseColor("#FF9800"))
                filePathInput.setText(path)
                openButton.isEnabled = true
                analyzeButton.isEnabled = true
                return
            }
        }
    }

    private fun openPdfFile() {
        try {
            val filePath = filePathInput.text.toString().trim()
            if (filePath.isEmpty()) {
                Toast.makeText(this, "Please enter a file path", Toast.LENGTH_SHORT).show()
                return
            }

            Log.d("QuickPDF", "📖 Opening PDF: $filePath")
            
            val intent = Intent(this, PdfReaderActivity::class.java)
            intent.putExtra("PDF_PATH", filePath)
            startActivity(intent)
            
        } catch (e: Exception) {
            Log.e("QuickPDF", "❌ Error opening PDF: ${e.message}")
            Toast.makeText(this, "Error opening PDF: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun analyzePdfFile() {
        try {
            val filePath = filePathInput.text.toString().trim()
            if (filePath.isEmpty()) {
                Toast.makeText(this, "Please enter a file path", Toast.LENGTH_SHORT).show()
                return
            }

            Log.d("QuickPDF", "🔍 Analyzing PDF: $filePath")
            
            val intent = Intent(this, PdfAnalysisActivity::class.java)
            intent.putExtra("PDF_PATH", filePath)
            startActivity(intent)
            
        } catch (e: Exception) {
            Log.e("QuickPDF", "❌ Error analyzing PDF: ${e.message}")
            Toast.makeText(this, "Error analyzing PDF: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
}
