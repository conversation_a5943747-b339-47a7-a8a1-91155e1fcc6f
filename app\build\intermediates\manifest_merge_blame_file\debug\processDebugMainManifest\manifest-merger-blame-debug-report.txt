1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tech.ekvayu"
4    android:versionCode="5"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:6:5-8:47
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:7:9-69
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
14    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:5-71
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:22-68
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:5-79
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:22-76
16    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:5-75
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:22-72
17    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:5-74
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:22-71
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:5-76
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:22-73
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:5-80
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:22-78
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:5-80
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:22-77
22    <uses-permission
22-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:5-21:38
23        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
23-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:22-79
24        android:minSdkVersion="30" />
24-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:21:9-35
25    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:5-77
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:22-74
26    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
27    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:5-94
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:22-92
28    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
29
30    <permission
30-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:29:5-112:19
37        android:name="com.tech.ekvayu.BaseClass.MyApplication"
37-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:30:9-48
38        android:allowBackup="true"
38-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:31:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:32:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:33:9-54
44        android:icon="@mipmap/ic_launcher"
44-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:34:9-43
45        android:label="@string/app_name"
45-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:35:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:36:9-54
47        android:supportsRtl="true"
47-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:37:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.Ekvayu"
49-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:38:9-44
50        android:usesCleartextTraffic="true" >
50-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:39:9-44
51        <activity
51-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:42:9-44:40
52            android:name="com.tech.ekvayu.Activities.MainActivity"
52-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:43:13-52
53            android:exported="false" />
53-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:44:13-37
54        <activity
54-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:46:9-48:40
55            android:name="com.tech.ekvayu.Activities.YahooAuth"
55-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:47:13-49
56            android:exported="false" />
56-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:48:13-37
57        <activity
57-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:49:9-70:20
58            android:name="com.tech.ekvayu.Activities.DashboardActivity"
58-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:50:13-57
59            android:exported="true" >
59-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:51:13-36
60
61            <!-- Main launcher intent filter -->
62            <intent-filter>
62-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:13-58:29
63                <action android:name="android.intent.action.MAIN" />
63-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-69
63-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:25-66
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:57:17-77
65-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:57:27-74
66            </intent-filter>
67            <!-- OAuth redirect intent filter -->
68            <intent-filter>
68-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:13-69:29
69                <action android:name="android.intent.action.VIEW" />
69-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:17-69
69-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:25-66
70
71                <category android:name="android.intent.category.DEFAULT" />
71-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:17-76
71-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:27-73
72                <category android:name="android.intent.category.BROWSABLE" />
72-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:17-78
72-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:27-75
73
74                <data
74-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-68:56
75                    android:host="oauth2redirect"
75-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:21-50
76                    android:scheme="com.tech.ekvayu" />
76-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:68:21-53
77            </intent-filter>
78        </activity>
79
80        <service
80-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:72:9-83:19
81            android:name="com.tech.ekvayu.EkService.NewGmailAccessibilityService"
81-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:73:13-67
82            android:enabled="true"
82-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:74:13-35
83            android:exported="true"
83-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:13-36
84            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
84-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:76:13-79
85            <intent-filter>
85-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:77:13-79:29
86                <action android:name="android.accessibilityservice.AccessibilityService" />
86-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:17-92
86-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:25-89
87            </intent-filter>
88
89            <meta-data
89-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:80:13-82:78
90                android:name="android.accessibilityservice"
90-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:81:17-60
91                android:resource="@xml/gmail_accessibility_service_config" />
91-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:82:17-75
92        </service>
93        <service
93-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:84:9-96:19
94            android:name="com.tech.ekvayu.EkService.YahooAccessibilityService"
94-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:85:13-64
95            android:enabled="true"
95-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:86:13-35
96            android:exported="true"
96-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:87:13-36
97            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
97-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:88:13-79
98            <intent-filter>
98-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:77:13-79:29
99                <action android:name="android.accessibilityservice.AccessibilityService" />
99-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:17-92
99-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:25-89
100            </intent-filter>
101
102            <meta-data
102-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:80:13-82:78
103                android:name="android.accessibilityservice"
103-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:81:17-60
104                android:resource="@xml/yahoo_accessibility_service_config" />
104-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:82:17-75
105        </service>
106        <service
106-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:97:9-108:19
107            android:name="com.tech.ekvayu.EkService.OutlookAccessibilityService"
107-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:98:13-66
108            android:exported="true"
108-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:99:13-36
109            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
109-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:100:13-79
110            <intent-filter>
110-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:77:13-79:29
111                <action android:name="android.accessibilityservice.AccessibilityService" />
111-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:17-92
111-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:25-89
112            </intent-filter>
113
114            <meta-data
114-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:80:13-82:78
115                android:name="android.accessibilityservice"
115-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:81:17-60
116                android:resource="@xml/outlook_accessibility_service_config" />
116-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:82:17-75
117        </service> <!-- Google Play Services Auth metadata -->
118        <meta-data
118-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:109:9-111:69
119            android:name="com.google.android.gms.version"
119-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:110:13-58
120            android:value="@integer/google_play_services_version" />
120-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:111:13-66
121
122        <provider
122-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
123            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
123-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
124            android:authorities="com.tech.ekvayu.mlkitinitprovider"
124-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
125            android:exported="false"
125-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
126            android:initOrder="99" />
126-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
127
128        <service
128-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
129            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
129-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:16:13-91
130            android:directBootAware="true"
130-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
131            android:exported="false" >
131-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:18:13-37
132            <meta-data
132-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
133                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
133-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
135        </service>
136
137        <activity
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
138            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
139            android:excludeFromRecents="true"
139-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
140            android:exported="true"
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
141            android:launchMode="singleTask"
141-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
142            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
142-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
143            <intent-filter>
143-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
144                <action android:name="android.intent.action.VIEW" />
144-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:17-69
144-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:25-66
145
146                <category android:name="android.intent.category.DEFAULT" />
146-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:17-76
146-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:27-73
147                <category android:name="android.intent.category.BROWSABLE" />
147-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:17-78
147-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:27-75
148
149                <data
149-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-68:56
150                    android:host="firebase.auth"
150-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:21-50
151                    android:path="/"
152                    android:scheme="genericidp" />
152-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:68:21-53
153            </intent-filter>
154        </activity>
155        <activity
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
156            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
157            android:excludeFromRecents="true"
157-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
158            android:exported="true"
158-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
159            android:launchMode="singleTask"
159-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
160            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
160-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
161            <intent-filter>
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
162                <action android:name="android.intent.action.VIEW" />
162-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:17-69
162-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:25-66
163
164                <category android:name="android.intent.category.DEFAULT" />
164-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:17-76
164-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:27-73
165                <category android:name="android.intent.category.BROWSABLE" />
165-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:17-78
165-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:27-75
166
167                <data
167-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-68:56
168                    android:host="firebase.auth"
168-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:21-50
169                    android:path="/"
170                    android:scheme="recaptcha" />
170-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:68:21-53
171            </intent-filter>
172        </activity>
173
174        <service
174-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
175            android:name="com.google.firebase.components.ComponentDiscoveryService"
175-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
176            android:directBootAware="true"
176-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
177            android:exported="false" >
177-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
178            <meta-data
178-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
179                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
179-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
181            <meta-data
181-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
182                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
182-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
184            <meta-data
184-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
185                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
185-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
187        </service>
188
189        <activity
189-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
190            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
190-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
191            android:excludeFromRecents="true"
191-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
192            android:exported="false"
192-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
193            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
193-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
194        <!--
195            Service handling Google Sign-In user revocation. For apps that do not integrate with
196            Google Sign-In, this service will never be started.
197        -->
198        <service
198-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
199            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
199-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
200            android:exported="true"
200-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
201            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
201-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
202            android:visibleToInstantApps="true" />
202-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
203
204        <activity
204-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
205            android:name="com.google.android.gms.common.api.GoogleApiActivity"
205-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
206            android:exported="false"
206-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
207            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
207-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
208
209        <provider
209-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
210            android:name="com.google.firebase.provider.FirebaseInitProvider"
210-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
211            android:authorities="com.tech.ekvayu.firebaseinitprovider"
211-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
212            android:directBootAware="true"
212-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
213            android:exported="false"
213-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
214            android:initOrder="100" />
214-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
215        <provider
215-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
216            android:name="androidx.startup.InitializationProvider"
216-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
217            android:authorities="com.tech.ekvayu.androidx-startup"
217-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
218            android:exported="false" >
218-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
219            <meta-data
219-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
220                android:name="androidx.emoji2.text.EmojiCompatInitializer"
220-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
221                android:value="androidx.startup" />
221-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
222            <meta-data
222-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
223                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
223-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
224                android:value="androidx.startup" />
224-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
225            <meta-data
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
226                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
226-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
227                android:value="androidx.startup" />
227-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
228        </provider>
229
230        <receiver
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
231            android:name="androidx.profileinstaller.ProfileInstallReceiver"
231-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
232            android:directBootAware="false"
232-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
233            android:enabled="true"
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
234            android:exported="true"
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
235            android:permission="android.permission.DUMP" >
235-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
237                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
240                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
243                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
244            </intent-filter>
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
246                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
246-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
246-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
247            </intent-filter>
248        </receiver>
249
250        <service
250-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
251            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
251-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
252            android:exported="false" >
252-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
253            <meta-data
253-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
254                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
254-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
255                android:value="cct" />
255-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
256        </service>
257        <service
257-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
258            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
258-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
259            android:exported="false"
259-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
260            android:permission="android.permission.BIND_JOB_SERVICE" >
260-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
261        </service>
262
263        <receiver
263-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
264            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
264-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
265            android:exported="false" />
265-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
266    </application>
267
268</manifest>
