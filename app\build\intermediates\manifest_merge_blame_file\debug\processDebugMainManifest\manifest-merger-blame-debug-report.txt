1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tech.ekvayu"
4    android:versionCode="5"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:6:5-8:47
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:7:9-69
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
14    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:5-71
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:22-68
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:5-79
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:22-76
16    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:5-75
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:22-72
17    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:5-74
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:22-71
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:5-76
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:22-73
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:5-80
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:22-78
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:5-80
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:22-77
22    <uses-permission
22-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:5-21:38
23        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
23-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:22-79
24        android:minSdkVersion="30" />
24-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:21:9-35
25    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:5-77
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:22-74
26    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
27    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:5-94
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:22-92
28    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
29
30    <permission
30-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:29:5-186:19
37        android:name="com.tech.ekvayu.BaseClass.MyApplication"
37-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:30:9-48
38        android:allowBackup="true"
38-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:31:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:32:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:33:9-54
44        android:icon="@mipmap/ic_launcher"
44-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:34:9-43
45        android:label="@string/app_name"
45-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:35:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:36:9-54
47        android:supportsRtl="true"
47-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:37:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.Ekvayu"
49-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:38:9-44
50        android:usesCleartextTraffic="true" >
50-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:39:9-44
51        <activity
51-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:42:9-44:40
52            android:name="com.tech.ekvayu.Activities.MainActivity"
52-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:43:13-52
53            android:exported="false" />
53-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:44:13-37
54
55        <!-- PDF Reader Activity -->
56        <activity
56-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:47:9-57:20
57            android:name="com.tech.ekvayu.PdfReaderActivity"
57-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:48:13-46
58            android:exported="false"
58-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:49:13-37
59            android:label="PDF Reader"
59-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:51:13-39
60            android:theme="@style/Theme.Ekvayu" >
60-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:50:13-48
61            <intent-filter>
61-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:52:13-56:29
62                <action android:name="android.intent.action.VIEW" />
62-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
62-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
63
64                <category android:name="android.intent.category.DEFAULT" />
64-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
64-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
65
66                <data android:mimeType="application/pdf" />
66-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
66-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:23-57
67            </intent-filter>
68        </activity>
69
70        <!-- PDF Test Activity -->
71        <activity
71-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:9-69:20
72            android:name="com.tech.ekvayu.PdfTestActivity"
72-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:13-44
73            android:exported="true"
73-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:62:13-36
74            android:label="PDF Test Viewer"
74-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:13-44
75            android:theme="@style/Theme.Ekvayu" >
75-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:13-48
76            <intent-filter>
76-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
77                <action android:name="android.intent.action.MAIN" />
77-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
77-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
79-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
80            </intent-filter>
81        </activity>
82
83        <!-- PDF Analysis Activity -->
84        <activity
84-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:72:9-81:20
85            android:name="com.tech.ekvayu.PdfAnalysisActivity"
85-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:73:13-48
86            android:exported="true"
86-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:74:13-36
87            android:label="PDF Content Analyzer"
87-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:76:13-49
88            android:theme="@style/Theme.Ekvayu" >
88-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:13-48
89            <intent-filter>
89-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
90                <action android:name="android.intent.action.MAIN" />
90-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
90-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
91
92                <category android:name="android.intent.category.LAUNCHER" />
92-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
92-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
93            </intent-filter>
94        </activity>
95
96        <!-- Notification Test Activity -->
97        <activity
97-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:84:9-93:20
98            android:name="com.tech.ekvayu.NotificationTestActivity"
98-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:85:13-53
99            android:exported="true"
99-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:86:13-36
100            android:label="Notification Test"
100-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:88:13-46
101            android:theme="@style/Theme.Ekvayu" >
101-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:87:13-48
102            <intent-filter>
102-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
103                <action android:name="android.intent.action.MAIN" />
103-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
103-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
104
105                <category android:name="android.intent.category.LAUNCHER" />
105-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
105-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
106            </intent-filter>
107        </activity>
108
109        <!-- Quick PDF Opener Activity -->
110        <activity
110-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:96:9-105:20
111            android:name="com.tech.ekvayu.QuickPdfOpenerActivity"
111-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:97:13-51
112            android:exported="true"
112-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:98:13-36
113            android:label="Quick PDF Opener"
113-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:100:13-45
114            android:theme="@style/Theme.Ekvayu" >
114-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:99:13-48
115            <intent-filter>
115-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
116                <action android:name="android.intent.action.MAIN" />
116-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
116-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
117
118                <category android:name="android.intent.category.LAUNCHER" />
118-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
118-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
119            </intent-filter>
120        </activity>
121        <activity
121-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:107:9-109:40
122            android:name="com.tech.ekvayu.Activities.YahooAuth"
122-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:108:13-49
123            android:exported="false" />
123-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:109:13-37
124        <activity
124-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:110:9-131:20
125            android:name="com.tech.ekvayu.Activities.DashboardActivity"
125-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:111:13-57
126            android:exported="true" >
126-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:112:13-36
127
128            <!-- Main launcher intent filter -->
129            <intent-filter>
129-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
130                <action android:name="android.intent.action.MAIN" />
130-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
130-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
131
132                <category android:name="android.intent.category.LAUNCHER" />
132-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
132-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
133            </intent-filter>
134            <!-- OAuth redirect intent filter -->
135            <intent-filter>
135-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:121:13-130:29
136                <action android:name="android.intent.action.VIEW" />
136-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
136-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
137
138                <category android:name="android.intent.category.DEFAULT" />
138-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
138-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
139                <category android:name="android.intent.category.BROWSABLE" />
139-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:125:17-78
139-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:125:27-75
140
141                <data
141-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
142                    android:host="oauth2redirect"
142-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:128:21-50
143                    android:scheme="com.tech.ekvayu" />
143-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:129:21-53
144            </intent-filter>
145        </activity>
146
147        <service
147-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:133:9-144:19
148            android:name="com.tech.ekvayu.EkService.NewGmailAccessibilityService"
148-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:134:13-67
149            android:enabled="true"
149-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:135:13-35
150            android:exported="true"
150-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:136:13-36
151            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
151-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:137:13-79
152            <intent-filter>
152-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:138:13-140:29
153                <action android:name="android.accessibilityservice.AccessibilityService" />
153-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:139:17-92
153-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:139:25-89
154            </intent-filter>
155
156            <meta-data
156-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:141:13-143:78
157                android:name="android.accessibilityservice"
157-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:142:17-60
158                android:resource="@xml/gmail_accessibility_service_config" />
158-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:143:17-75
159        </service>
160        <service
160-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:145:9-157:19
161            android:name="com.tech.ekvayu.EkService.YahooAccessibilityService"
161-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:146:13-64
162            android:enabled="true"
162-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:147:13-35
163            android:exported="true"
163-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:148:13-36
164            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
164-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:149:13-79
165            <intent-filter>
165-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:138:13-140:29
166                <action android:name="android.accessibilityservice.AccessibilityService" />
166-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:139:17-92
166-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:139:25-89
167            </intent-filter>
168
169            <meta-data
169-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:141:13-143:78
170                android:name="android.accessibilityservice"
170-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:142:17-60
171                android:resource="@xml/yahoo_accessibility_service_config" />
171-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:143:17-75
172        </service>
173        <service
173-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:158:9-169:19
174            android:name="com.tech.ekvayu.EkService.OutlookAccessibilityService"
174-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:159:13-66
175            android:exported="true"
175-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:160:13-36
176            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
176-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:161:13-79
177            <intent-filter>
177-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:138:13-140:29
178                <action android:name="android.accessibilityservice.AccessibilityService" />
178-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:139:17-92
178-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:139:25-89
179            </intent-filter>
180
181            <meta-data
181-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:141:13-143:78
182                android:name="android.accessibilityservice"
182-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:142:17-60
183                android:resource="@xml/outlook_accessibility_service_config" />
183-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:143:17-75
184        </service>
185
186        <!-- FileProvider for sharing PDF files -->
187        <provider
188            android:name="androidx.core.content.FileProvider"
188-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:173:13-62
189            android:authorities="com.tech.ekvayu.fileprovider"
189-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:174:13-64
190            android:exported="false"
190-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:175:13-37
191            android:grantUriPermissions="true" >
191-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:176:13-47
192            <meta-data
192-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:177:13-179:54
193                android:name="android.support.FILE_PROVIDER_PATHS"
193-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:178:17-67
194                android:resource="@xml/file_paths" />
194-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:179:17-51
195        </provider>
196
197        <!-- Google Play Services Auth metadata -->
198        <meta-data
198-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:183:9-185:69
199            android:name="com.google.android.gms.version"
199-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:184:13-58
200            android:value="@integer/google_play_services_version" />
200-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:185:13-66
201
202        <provider
202-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
203            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
203-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
204            android:authorities="com.tech.ekvayu.mlkitinitprovider"
204-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
205            android:exported="false"
205-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
206            android:initOrder="99" />
206-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
207
208        <service
208-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
209            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
209-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:16:13-91
210            android:directBootAware="true"
210-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
211            android:exported="false" >
211-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:18:13-37
212            <meta-data
212-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
213                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
213-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
215        </service>
216
217        <activity
217-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
218            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
218-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
219            android:excludeFromRecents="true"
219-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
220            android:exported="true"
220-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
221            android:launchMode="singleTask"
221-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
222            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
222-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
223            <intent-filter>
223-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
224                <action android:name="android.intent.action.VIEW" />
224-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
224-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
225
226                <category android:name="android.intent.category.DEFAULT" />
226-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
226-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
227                <category android:name="android.intent.category.BROWSABLE" />
227-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:125:17-78
227-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:125:27-75
228
229                <data
229-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
230                    android:host="firebase.auth"
230-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:128:21-50
231                    android:path="/"
232                    android:scheme="genericidp" />
232-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:129:21-53
233            </intent-filter>
234        </activity>
235        <activity
235-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
236            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
236-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
237            android:excludeFromRecents="true"
237-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
238            android:exported="true"
238-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
239            android:launchMode="singleTask"
239-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
240            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
240-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
241            <intent-filter>
241-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
242                <action android:name="android.intent.action.VIEW" />
242-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
242-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
243
244                <category android:name="android.intent.category.DEFAULT" />
244-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
244-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
245                <category android:name="android.intent.category.BROWSABLE" />
245-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:125:17-78
245-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:125:27-75
246
247                <data
247-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
248                    android:host="firebase.auth"
248-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:128:21-50
249                    android:path="/"
250                    android:scheme="recaptcha" />
250-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:129:21-53
251            </intent-filter>
252        </activity>
253
254        <service
254-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
255            android:name="com.google.firebase.components.ComponentDiscoveryService"
255-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
256            android:directBootAware="true"
256-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
257            android:exported="false" >
257-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
258            <meta-data
258-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
259                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
259-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
260                android:value="com.google.firebase.components.ComponentRegistrar" />
260-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
261            <meta-data
261-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
262                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
262-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
263                android:value="com.google.firebase.components.ComponentRegistrar" />
263-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
264            <meta-data
264-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
265                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
265-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
266                android:value="com.google.firebase.components.ComponentRegistrar" />
266-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
267        </service>
268
269        <activity
269-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
270            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
270-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
271            android:excludeFromRecents="true"
271-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
272            android:exported="false"
272-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
273            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
273-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
274        <!--
275            Service handling Google Sign-In user revocation. For apps that do not integrate with
276            Google Sign-In, this service will never be started.
277        -->
278        <service
278-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
279            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
279-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
280            android:exported="true"
280-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
281            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
281-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
282            android:visibleToInstantApps="true" />
282-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
283
284        <activity
284-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
285            android:name="com.google.android.gms.common.api.GoogleApiActivity"
285-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
286            android:exported="false"
286-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
287            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
287-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
288
289        <provider
289-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
290            android:name="com.google.firebase.provider.FirebaseInitProvider"
290-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
291            android:authorities="com.tech.ekvayu.firebaseinitprovider"
291-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
292            android:directBootAware="true"
292-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
293            android:exported="false"
293-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
294            android:initOrder="100" />
294-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
295        <provider
295-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
296            android:name="androidx.startup.InitializationProvider"
296-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
297            android:authorities="com.tech.ekvayu.androidx-startup"
297-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
298            android:exported="false" >
298-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
299            <meta-data
299-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
300                android:name="androidx.emoji2.text.EmojiCompatInitializer"
300-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
301                android:value="androidx.startup" />
301-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
302            <meta-data
302-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
303                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
303-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
304                android:value="androidx.startup" />
304-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
305            <meta-data
305-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
306                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
306-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
307                android:value="androidx.startup" />
307-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
308        </provider>
309
310        <receiver
310-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
311            android:name="androidx.profileinstaller.ProfileInstallReceiver"
311-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
312            android:directBootAware="false"
312-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
313            android:enabled="true"
313-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
314            android:exported="true"
314-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
315            android:permission="android.permission.DUMP" >
315-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
316            <intent-filter>
316-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
317                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
317-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
317-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
318            </intent-filter>
319            <intent-filter>
319-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
320                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
320-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
320-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
321            </intent-filter>
322            <intent-filter>
322-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
323                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
323-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
323-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
324            </intent-filter>
325            <intent-filter>
325-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
326                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
326-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
326-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
327            </intent-filter>
328        </receiver>
329
330        <service
330-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
331            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
331-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
332            android:exported="false" >
332-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
333            <meta-data
333-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
334                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
334-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
335                android:value="cct" />
335-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
336        </service>
337        <service
337-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
338            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
338-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
339            android:exported="false"
339-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
340            android:permission="android.permission.BIND_JOB_SERVICE" >
340-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
341        </service>
342
343        <receiver
343-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
344            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
344-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
345            android:exported="false" />
345-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
346    </application>
347
348</manifest>
