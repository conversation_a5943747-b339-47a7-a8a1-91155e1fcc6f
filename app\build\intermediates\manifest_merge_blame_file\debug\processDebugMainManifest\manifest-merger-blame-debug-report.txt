1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tech.ekvayu"
4    android:versionCode="5"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:6:5-8:47
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:7:9-69
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
14    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:5-71
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:22-68
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:5-79
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:22-76
16    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:5-75
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:22-72
17    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:5-74
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:22-71
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:5-76
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:22-73
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:5-80
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:22-78
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:5-80
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:22-77
22    <uses-permission
22-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:5-21:38
23        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
23-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:22-79
24        android:minSdkVersion="30" />
24-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:21:9-35
25    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:5-77
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:22-74
26    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
27    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:5-94
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:22-92
28    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
29
30    <permission
30-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:29:5-125:19
37        android:name="com.tech.ekvayu.BaseClass.MyApplication"
37-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:30:9-48
38        android:allowBackup="true"
38-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:31:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:32:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:33:9-54
44        android:icon="@mipmap/ic_launcher"
44-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:34:9-43
45        android:label="@string/app_name"
45-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:35:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:36:9-54
47        android:supportsRtl="true"
47-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:37:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.Ekvayu"
49-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:38:9-44
50        android:usesCleartextTraffic="true" >
50-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:39:9-44
51        <activity
51-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:42:9-44:40
52            android:name="com.tech.ekvayu.Activities.MainActivity"
52-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:43:13-52
53            android:exported="false" />
53-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:44:13-37
54        <activity
54-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:46:9-48:40
55            android:name="com.tech.ekvayu.Activities.YahooAuth"
55-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:47:13-49
56            android:exported="false" />
56-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:48:13-37
57        <activity
57-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:49:9-70:20
58            android:name="com.tech.ekvayu.Activities.DashboardActivity"
58-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:50:13-57
59            android:exported="true" >
59-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:51:13-36
60
61            <!-- Main launcher intent filter -->
62            <intent-filter>
62-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:13-58:29
63                <action android:name="android.intent.action.MAIN" />
63-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-69
63-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:25-66
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:57:17-77
65-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:57:27-74
66            </intent-filter>
67            <!-- OAuth redirect intent filter -->
68            <intent-filter>
68-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:13-69:29
69                <action android:name="android.intent.action.VIEW" />
69-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:17-69
69-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:25-66
70
71                <category android:name="android.intent.category.DEFAULT" />
71-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:17-76
71-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:27-73
72                <category android:name="android.intent.category.BROWSABLE" />
72-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:17-78
72-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:27-75
73
74                <data
74-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-68:56
75                    android:host="oauth2redirect"
75-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:21-50
76                    android:scheme="com.tech.ekvayu" />
76-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:68:21-53
77            </intent-filter>
78        </activity>
79
80        <service
80-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:72:9-83:19
81            android:name="com.tech.ekvayu.EkService.NewGmailAccessibilityService"
81-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:73:13-67
82            android:enabled="true"
82-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:74:13-35
83            android:exported="true"
83-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:13-36
84            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
84-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:76:13-79
85            <intent-filter>
85-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:77:13-79:29
86                <action android:name="android.accessibilityservice.AccessibilityService" />
86-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:17-92
86-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:25-89
87            </intent-filter>
88
89            <meta-data
89-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:80:13-82:78
90                android:name="android.accessibilityservice"
90-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:81:17-60
91                android:resource="@xml/gmail_accessibility_service_config" />
91-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:82:17-75
92        </service>
93        <service
93-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:84:9-96:19
94            android:name="com.tech.ekvayu.EkService.YahooAccessibilityService"
94-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:85:13-64
95            android:enabled="true"
95-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:86:13-35
96            android:exported="true"
96-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:87:13-36
97            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
97-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:88:13-79
98            <intent-filter>
98-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:77:13-79:29
99                <action android:name="android.accessibilityservice.AccessibilityService" />
99-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:17-92
99-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:25-89
100            </intent-filter>
101
102            <meta-data
102-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:80:13-82:78
103                android:name="android.accessibilityservice"
103-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:81:17-60
104                android:resource="@xml/yahoo_accessibility_service_config" />
104-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:82:17-75
105        </service>
106        <service
106-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:97:9-108:19
107            android:name="com.tech.ekvayu.EkService.OutlookAccessibilityService"
107-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:98:13-66
108            android:exported="true"
108-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:99:13-36
109            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
109-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:100:13-79
110            <intent-filter>
110-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:77:13-79:29
111                <action android:name="android.accessibilityservice.AccessibilityService" />
111-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:17-92
111-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:25-89
112            </intent-filter>
113
114            <meta-data
114-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:80:13-82:78
115                android:name="android.accessibilityservice"
115-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:81:17-60
116                android:resource="@xml/outlook_accessibility_service_config" />
116-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:82:17-75
117        </service>
118
119        <!-- FileProvider for sharing PDF files -->
120        <provider
121            android:name="androidx.core.content.FileProvider"
121-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:112:13-62
122            android:authorities="com.tech.ekvayu.fileprovider"
122-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:113:13-64
123            android:exported="false"
123-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:114:13-37
124            android:grantUriPermissions="true" >
124-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:115:13-47
125            <meta-data
125-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:116:13-118:54
126                android:name="android.support.FILE_PROVIDER_PATHS"
126-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:117:17-67
127                android:resource="@xml/file_paths" />
127-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:118:17-51
128        </provider>
129
130        <!-- Google Play Services Auth metadata -->
131        <meta-data
131-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:122:9-124:69
132            android:name="com.google.android.gms.version"
132-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:123:13-58
133            android:value="@integer/google_play_services_version" />
133-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:124:13-66
134
135        <provider
135-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
136            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
136-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
137            android:authorities="com.tech.ekvayu.mlkitinitprovider"
137-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
138            android:exported="false"
138-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
139            android:initOrder="99" />
139-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
140
141        <service
141-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
142            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
142-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:16:13-91
143            android:directBootAware="true"
143-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
144            android:exported="false" >
144-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:18:13-37
145            <meta-data
145-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
146                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
146-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
148        </service>
149
150        <activity
150-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
151            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
151-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
152            android:excludeFromRecents="true"
152-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
153            android:exported="true"
153-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
154            android:launchMode="singleTask"
154-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
155            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
156            <intent-filter>
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
157                <action android:name="android.intent.action.VIEW" />
157-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:17-69
157-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:25-66
158
159                <category android:name="android.intent.category.DEFAULT" />
159-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:17-76
159-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:27-73
160                <category android:name="android.intent.category.BROWSABLE" />
160-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:17-78
160-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:27-75
161
162                <data
162-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-68:56
163                    android:host="firebase.auth"
163-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:21-50
164                    android:path="/"
165                    android:scheme="genericidp" />
165-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:68:21-53
166            </intent-filter>
167        </activity>
168        <activity
168-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
169            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
169-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
170            android:excludeFromRecents="true"
170-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
171            android:exported="true"
171-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
172            android:launchMode="singleTask"
172-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
173            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
173-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
174            <intent-filter>
174-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
175                <action android:name="android.intent.action.VIEW" />
175-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:17-69
175-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:25-66
176
177                <category android:name="android.intent.category.DEFAULT" />
177-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:17-76
177-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:27-73
178                <category android:name="android.intent.category.BROWSABLE" />
178-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:17-78
178-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:27-75
179
180                <data
180-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-68:56
181                    android:host="firebase.auth"
181-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:21-50
182                    android:path="/"
183                    android:scheme="recaptcha" />
183-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:68:21-53
184            </intent-filter>
185        </activity>
186
187        <service
187-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
188            android:name="com.google.firebase.components.ComponentDiscoveryService"
188-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
189            android:directBootAware="true"
189-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
190            android:exported="false" >
190-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
191            <meta-data
191-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
192                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
192-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
194            <meta-data
194-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
195                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
195-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
197            <meta-data
197-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
198                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
198-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
199                android:value="com.google.firebase.components.ComponentRegistrar" />
199-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
200        </service>
201
202        <activity
202-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
203            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
203-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
204            android:excludeFromRecents="true"
204-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
205            android:exported="false"
205-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
206            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
206-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
207        <!--
208            Service handling Google Sign-In user revocation. For apps that do not integrate with
209            Google Sign-In, this service will never be started.
210        -->
211        <service
211-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
212            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
212-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
213            android:exported="true"
213-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
214            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
214-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
215            android:visibleToInstantApps="true" />
215-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
216
217        <activity
217-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
218            android:name="com.google.android.gms.common.api.GoogleApiActivity"
218-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
219            android:exported="false"
219-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
220            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
220-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
221
222        <provider
222-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
223            android:name="com.google.firebase.provider.FirebaseInitProvider"
223-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
224            android:authorities="com.tech.ekvayu.firebaseinitprovider"
224-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
225            android:directBootAware="true"
225-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
226            android:exported="false"
226-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
227            android:initOrder="100" />
227-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
228        <provider
228-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
229            android:name="androidx.startup.InitializationProvider"
229-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
230            android:authorities="com.tech.ekvayu.androidx-startup"
230-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
231            android:exported="false" >
231-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
232            <meta-data
232-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
233                android:name="androidx.emoji2.text.EmojiCompatInitializer"
233-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
234                android:value="androidx.startup" />
234-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
235            <meta-data
235-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
236                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
236-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
237                android:value="androidx.startup" />
237-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
238            <meta-data
238-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
239                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
240                android:value="androidx.startup" />
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
241        </provider>
242
243        <receiver
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
244            android:name="androidx.profileinstaller.ProfileInstallReceiver"
244-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
245            android:directBootAware="false"
245-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
246            android:enabled="true"
246-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
247            android:exported="true"
247-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
248            android:permission="android.permission.DUMP" >
248-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
250                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
250-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
250-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
251            </intent-filter>
252            <intent-filter>
252-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
253                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
253-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
253-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
254            </intent-filter>
255            <intent-filter>
255-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
256                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
256-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
256-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
257            </intent-filter>
258            <intent-filter>
258-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
259                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
259-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
259-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
260            </intent-filter>
261        </receiver>
262
263        <service
263-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
264            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
264-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
265            android:exported="false" >
265-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
266            <meta-data
266-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
267                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
267-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
268                android:value="cct" />
268-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
269        </service>
270        <service
270-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
271            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
271-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
272            android:exported="false"
272-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
273            android:permission="android.permission.BIND_JOB_SERVICE" >
273-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
274        </service>
275
276        <receiver
276-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
277            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
277-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
278            android:exported="false" />
278-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
279    </application>
280
281</manifest>
