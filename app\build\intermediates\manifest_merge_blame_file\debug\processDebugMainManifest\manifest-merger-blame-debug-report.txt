1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tech.ekvayu"
4    android:versionCode="5"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:6:5-8:47
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:7:9-69
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
14    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:5-71
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:22-68
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:5-79
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:22-76
16    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:5-75
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:22-72
17    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:5-74
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:22-71
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:5-76
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:22-73
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:5-80
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:22-78
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:5-80
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:22-77
22    <uses-permission
22-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:5-21:38
23        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
23-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:22-79
24        android:minSdkVersion="30" />
24-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:21:9-35
25    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:5-77
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:22-74
26    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
27    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:5-94
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:22-92
28    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
29
30    <permission
30-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:29:5-162:19
37        android:name="com.tech.ekvayu.BaseClass.MyApplication"
37-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:30:9-48
38        android:allowBackup="true"
38-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:31:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:32:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:33:9-54
44        android:icon="@mipmap/ic_launcher"
44-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:34:9-43
45        android:label="@string/app_name"
45-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:35:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:36:9-54
47        android:supportsRtl="true"
47-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:37:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.Ekvayu"
49-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:38:9-44
50        android:usesCleartextTraffic="true" >
50-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:39:9-44
51        <activity
51-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:42:9-44:40
52            android:name="com.tech.ekvayu.Activities.MainActivity"
52-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:43:13-52
53            android:exported="false" />
53-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:44:13-37
54
55        <!-- PDF Reader Activity -->
56        <activity
56-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:47:9-57:20
57            android:name="com.tech.ekvayu.PdfReaderActivity"
57-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:48:13-46
58            android:exported="false"
58-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:49:13-37
59            android:label="PDF Reader"
59-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:51:13-39
60            android:theme="@style/Theme.Ekvayu" >
60-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:50:13-48
61            <intent-filter>
61-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:52:13-56:29
62                <action android:name="android.intent.action.VIEW" />
62-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
62-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
63
64                <category android:name="android.intent.category.DEFAULT" />
64-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
64-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
65
66                <data android:mimeType="application/pdf" />
66-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
66-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:23-57
67            </intent-filter>
68        </activity>
69
70        <!-- PDF Test Activity -->
71        <activity
71-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:9-69:20
72            android:name="com.tech.ekvayu.PdfTestActivity"
72-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:13-44
73            android:exported="true"
73-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:62:13-36
74            android:label="PDF Test Viewer"
74-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:13-44
75            android:theme="@style/Theme.Ekvayu" >
75-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:13-48
76            <intent-filter>
76-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
77                <action android:name="android.intent.action.MAIN" />
77-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
77-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
79-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
80            </intent-filter>
81        </activity>
82
83        <!-- PDF Analysis Activity -->
84        <activity
84-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:72:9-81:20
85            android:name="com.tech.ekvayu.PdfAnalysisActivity"
85-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:73:13-48
86            android:exported="true"
86-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:74:13-36
87            android:label="PDF Content Analyzer"
87-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:76:13-49
88            android:theme="@style/Theme.Ekvayu" >
88-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:13-48
89            <intent-filter>
89-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
90                <action android:name="android.intent.action.MAIN" />
90-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
90-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
91
92                <category android:name="android.intent.category.LAUNCHER" />
92-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
92-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
93            </intent-filter>
94        </activity>
95        <activity
95-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:83:9-85:40
96            android:name="com.tech.ekvayu.Activities.YahooAuth"
96-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:84:13-49
97            android:exported="false" />
97-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:85:13-37
98        <activity
98-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:86:9-107:20
99            android:name="com.tech.ekvayu.Activities.DashboardActivity"
99-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:87:13-57
100            android:exported="true" >
100-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:88:13-36
101
102            <!-- Main launcher intent filter -->
103            <intent-filter>
103-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
104                <action android:name="android.intent.action.MAIN" />
104-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
104-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
105
106                <category android:name="android.intent.category.LAUNCHER" />
106-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
106-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
107            </intent-filter>
108            <!-- OAuth redirect intent filter -->
109            <intent-filter>
109-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:97:13-106:29
110                <action android:name="android.intent.action.VIEW" />
110-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
110-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
111
112                <category android:name="android.intent.category.DEFAULT" />
112-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
112-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
113                <category android:name="android.intent.category.BROWSABLE" />
113-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:101:17-78
113-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:101:27-75
114
115                <data
115-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
116                    android:host="oauth2redirect"
116-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:104:21-50
117                    android:scheme="com.tech.ekvayu" />
117-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:105:21-53
118            </intent-filter>
119        </activity>
120
121        <service
121-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:109:9-120:19
122            android:name="com.tech.ekvayu.EkService.NewGmailAccessibilityService"
122-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:110:13-67
123            android:enabled="true"
123-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:111:13-35
124            android:exported="true"
124-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:112:13-36
125            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
125-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:113:13-79
126            <intent-filter>
126-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:114:13-116:29
127                <action android:name="android.accessibilityservice.AccessibilityService" />
127-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:115:17-92
127-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:115:25-89
128            </intent-filter>
129
130            <meta-data
130-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:117:13-119:78
131                android:name="android.accessibilityservice"
131-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:118:17-60
132                android:resource="@xml/gmail_accessibility_service_config" />
132-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:119:17-75
133        </service>
134        <service
134-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:121:9-133:19
135            android:name="com.tech.ekvayu.EkService.YahooAccessibilityService"
135-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:122:13-64
136            android:enabled="true"
136-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:123:13-35
137            android:exported="true"
137-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:124:13-36
138            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
138-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:125:13-79
139            <intent-filter>
139-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:114:13-116:29
140                <action android:name="android.accessibilityservice.AccessibilityService" />
140-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:115:17-92
140-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:115:25-89
141            </intent-filter>
142
143            <meta-data
143-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:117:13-119:78
144                android:name="android.accessibilityservice"
144-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:118:17-60
145                android:resource="@xml/yahoo_accessibility_service_config" />
145-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:119:17-75
146        </service>
147        <service
147-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:134:9-145:19
148            android:name="com.tech.ekvayu.EkService.OutlookAccessibilityService"
148-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:135:13-66
149            android:exported="true"
149-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:136:13-36
150            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
150-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:137:13-79
151            <intent-filter>
151-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:114:13-116:29
152                <action android:name="android.accessibilityservice.AccessibilityService" />
152-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:115:17-92
152-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:115:25-89
153            </intent-filter>
154
155            <meta-data
155-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:117:13-119:78
156                android:name="android.accessibilityservice"
156-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:118:17-60
157                android:resource="@xml/outlook_accessibility_service_config" />
157-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:119:17-75
158        </service>
159
160        <!-- FileProvider for sharing PDF files -->
161        <provider
162            android:name="androidx.core.content.FileProvider"
162-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:149:13-62
163            android:authorities="com.tech.ekvayu.fileprovider"
163-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:150:13-64
164            android:exported="false"
164-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:151:13-37
165            android:grantUriPermissions="true" >
165-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:152:13-47
166            <meta-data
166-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:153:13-155:54
167                android:name="android.support.FILE_PROVIDER_PATHS"
167-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:154:17-67
168                android:resource="@xml/file_paths" />
168-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:155:17-51
169        </provider>
170
171        <!-- Google Play Services Auth metadata -->
172        <meta-data
172-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:159:9-161:69
173            android:name="com.google.android.gms.version"
173-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:160:13-58
174            android:value="@integer/google_play_services_version" />
174-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:161:13-66
175
176        <provider
176-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
177            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
177-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
178            android:authorities="com.tech.ekvayu.mlkitinitprovider"
178-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
179            android:exported="false"
179-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
180            android:initOrder="99" />
180-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
181
182        <service
182-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
183            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
183-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:16:13-91
184            android:directBootAware="true"
184-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
185            android:exported="false" >
185-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:18:13-37
186            <meta-data
186-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
187                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
187-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
188                android:value="com.google.firebase.components.ComponentRegistrar" />
188-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
189        </service>
190
191        <activity
191-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
192            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
192-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
193            android:excludeFromRecents="true"
193-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
194            android:exported="true"
194-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
195            android:launchMode="singleTask"
195-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
196            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
196-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
197            <intent-filter>
197-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
198                <action android:name="android.intent.action.VIEW" />
198-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
198-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
199
200                <category android:name="android.intent.category.DEFAULT" />
200-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
200-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
201                <category android:name="android.intent.category.BROWSABLE" />
201-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:101:17-78
201-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:101:27-75
202
203                <data
203-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
204                    android:host="firebase.auth"
204-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:104:21-50
205                    android:path="/"
206                    android:scheme="genericidp" />
206-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:105:21-53
207            </intent-filter>
208        </activity>
209        <activity
209-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
210            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
210-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
211            android:excludeFromRecents="true"
211-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
212            android:exported="true"
212-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
213            android:launchMode="singleTask"
213-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
214            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
214-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
215            <intent-filter>
215-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
216                <action android:name="android.intent.action.VIEW" />
216-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
216-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
217
218                <category android:name="android.intent.category.DEFAULT" />
218-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
218-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
219                <category android:name="android.intent.category.BROWSABLE" />
219-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:101:17-78
219-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:101:27-75
220
221                <data
221-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
222                    android:host="firebase.auth"
222-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:104:21-50
223                    android:path="/"
224                    android:scheme="recaptcha" />
224-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:105:21-53
225            </intent-filter>
226        </activity>
227
228        <service
228-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
229            android:name="com.google.firebase.components.ComponentDiscoveryService"
229-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
230            android:directBootAware="true"
230-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
231            android:exported="false" >
231-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
232            <meta-data
232-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
233                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
233-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
234                android:value="com.google.firebase.components.ComponentRegistrar" />
234-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
235            <meta-data
235-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
236                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
236-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
237                android:value="com.google.firebase.components.ComponentRegistrar" />
237-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
238            <meta-data
238-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
239                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
239-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
241        </service>
242
243        <activity
243-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
244            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
244-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
245            android:excludeFromRecents="true"
245-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
246            android:exported="false"
246-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
247            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
247-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
248        <!--
249            Service handling Google Sign-In user revocation. For apps that do not integrate with
250            Google Sign-In, this service will never be started.
251        -->
252        <service
252-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
253            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
253-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
254            android:exported="true"
254-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
255            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
255-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
256            android:visibleToInstantApps="true" />
256-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
257
258        <activity
258-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
259            android:name="com.google.android.gms.common.api.GoogleApiActivity"
259-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
260            android:exported="false"
260-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
261            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
261-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
262
263        <provider
263-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
264            android:name="com.google.firebase.provider.FirebaseInitProvider"
264-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
265            android:authorities="com.tech.ekvayu.firebaseinitprovider"
265-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
266            android:directBootAware="true"
266-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
267            android:exported="false"
267-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
268            android:initOrder="100" />
268-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
269        <provider
269-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
270            android:name="androidx.startup.InitializationProvider"
270-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
271            android:authorities="com.tech.ekvayu.androidx-startup"
271-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
272            android:exported="false" >
272-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
273            <meta-data
273-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
274                android:name="androidx.emoji2.text.EmojiCompatInitializer"
274-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
275                android:value="androidx.startup" />
275-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
276            <meta-data
276-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
277                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
277-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
278                android:value="androidx.startup" />
278-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
279            <meta-data
279-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
280                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
280-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
281                android:value="androidx.startup" />
281-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
282        </provider>
283
284        <receiver
284-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
285            android:name="androidx.profileinstaller.ProfileInstallReceiver"
285-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
286            android:directBootAware="false"
286-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
287            android:enabled="true"
287-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
288            android:exported="true"
288-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
289            android:permission="android.permission.DUMP" >
289-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
290            <intent-filter>
290-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
291                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
291-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
291-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
292            </intent-filter>
293            <intent-filter>
293-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
294                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
294-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
294-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
295            </intent-filter>
296            <intent-filter>
296-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
297                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
297-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
297-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
298            </intent-filter>
299            <intent-filter>
299-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
300                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
300-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
300-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
301            </intent-filter>
302        </receiver>
303
304        <service
304-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
305            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
305-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
306            android:exported="false" >
306-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
307            <meta-data
307-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
308                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
308-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
309                android:value="cct" />
309-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
310        </service>
311        <service
311-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
312            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
312-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
313            android:exported="false"
313-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
314            android:permission="android.permission.BIND_JOB_SERVICE" >
314-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
315        </service>
316
317        <receiver
317-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
318            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
318-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
319            android:exported="false" />
319-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
320    </application>
321
322</manifest>
