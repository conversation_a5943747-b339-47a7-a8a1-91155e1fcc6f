1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tech.ekvayu"
4    android:versionCode="5"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:6:5-8:47
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:7:9-69
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
14    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:5-71
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:22-68
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:5-79
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:22-76
16    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:5-75
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:22-72
17    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:5-74
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:22-71
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:5-76
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:22-73
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:5-80
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:22-78
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:5-80
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:22-77
22    <uses-permission
22-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:5-21:38
23        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
23-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:20:22-79
24        android:minSdkVersion="30" />
24-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:21:9-35
25    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:5-77
25-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:22:22-74
26    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
26-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
27    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:5-94
27-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:22-92
28    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
28-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
29
30    <permission
30-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
35
36    <application
36-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:29:5-150:19
37        android:name="com.tech.ekvayu.BaseClass.MyApplication"
37-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:30:9-48
38        android:allowBackup="true"
38-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:31:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:32:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:33:9-54
44        android:icon="@mipmap/ic_launcher"
44-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:34:9-43
45        android:label="@string/app_name"
45-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:35:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:36:9-54
47        android:supportsRtl="true"
47-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:37:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.Ekvayu"
49-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:38:9-44
50        android:usesCleartextTraffic="true" >
50-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:39:9-44
51        <activity
51-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:42:9-44:40
52            android:name="com.tech.ekvayu.Activities.MainActivity"
52-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:43:13-52
53            android:exported="false" />
53-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:44:13-37
54
55        <!-- PDF Reader Activity -->
56        <activity
56-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:47:9-57:20
57            android:name="com.tech.ekvayu.PdfReaderActivity"
57-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:48:13-46
58            android:exported="false"
58-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:49:13-37
59            android:label="PDF Reader"
59-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:51:13-39
60            android:theme="@style/Theme.Ekvayu" >
60-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:50:13-48
61            <intent-filter>
61-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:52:13-56:29
62                <action android:name="android.intent.action.VIEW" />
62-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
62-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
63
64                <category android:name="android.intent.category.DEFAULT" />
64-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
64-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
65
66                <data android:mimeType="application/pdf" />
66-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
66-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:23-57
67            </intent-filter>
68        </activity>
69
70        <!-- PDF Test Activity -->
71        <activity
71-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:9-69:20
72            android:name="com.tech.ekvayu.PdfTestActivity"
72-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:13-44
73            android:exported="true"
73-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:62:13-36
74            android:label="PDF Test Viewer"
74-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:13-44
75            android:theme="@style/Theme.Ekvayu" >
75-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:13-48
76            <intent-filter>
76-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
77                <action android:name="android.intent.action.MAIN" />
77-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
77-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
79-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
80            </intent-filter>
81        </activity>
82        <activity
82-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:71:9-73:40
83            android:name="com.tech.ekvayu.Activities.YahooAuth"
83-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:72:13-49
84            android:exported="false" />
84-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:73:13-37
85        <activity
85-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:74:9-95:20
86            android:name="com.tech.ekvayu.Activities.DashboardActivity"
86-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:13-57
87            android:exported="true" >
87-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:76:13-36
88
89            <!-- Main launcher intent filter -->
90            <intent-filter>
90-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:13-68:29
91                <action android:name="android.intent.action.MAIN" />
91-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:17-69
91-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:66:25-66
92
93                <category android:name="android.intent.category.LAUNCHER" />
93-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:17-77
93-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:67:27-74
94            </intent-filter>
95            <!-- OAuth redirect intent filter -->
96            <intent-filter>
96-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:85:13-94:29
97                <action android:name="android.intent.action.VIEW" />
97-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
97-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
98
99                <category android:name="android.intent.category.DEFAULT" />
99-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
99-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
100                <category android:name="android.intent.category.BROWSABLE" />
100-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:89:17-78
100-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:89:27-75
101
102                <data
102-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
103                    android:host="oauth2redirect"
103-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:92:21-50
104                    android:scheme="com.tech.ekvayu" />
104-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:93:21-53
105            </intent-filter>
106        </activity>
107
108        <service
108-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:97:9-108:19
109            android:name="com.tech.ekvayu.EkService.NewGmailAccessibilityService"
109-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:98:13-67
110            android:enabled="true"
110-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:99:13-35
111            android:exported="true"
111-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:100:13-36
112            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
112-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:101:13-79
113            <intent-filter>
113-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:102:13-104:29
114                <action android:name="android.accessibilityservice.AccessibilityService" />
114-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:103:17-92
114-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:103:25-89
115            </intent-filter>
116
117            <meta-data
117-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:105:13-107:78
118                android:name="android.accessibilityservice"
118-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:106:17-60
119                android:resource="@xml/gmail_accessibility_service_config" />
119-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:107:17-75
120        </service>
121        <service
121-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:109:9-121:19
122            android:name="com.tech.ekvayu.EkService.YahooAccessibilityService"
122-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:110:13-64
123            android:enabled="true"
123-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:111:13-35
124            android:exported="true"
124-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:112:13-36
125            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
125-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:113:13-79
126            <intent-filter>
126-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:102:13-104:29
127                <action android:name="android.accessibilityservice.AccessibilityService" />
127-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:103:17-92
127-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:103:25-89
128            </intent-filter>
129
130            <meta-data
130-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:105:13-107:78
131                android:name="android.accessibilityservice"
131-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:106:17-60
132                android:resource="@xml/yahoo_accessibility_service_config" />
132-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:107:17-75
133        </service>
134        <service
134-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:122:9-133:19
135            android:name="com.tech.ekvayu.EkService.OutlookAccessibilityService"
135-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:123:13-66
136            android:exported="true"
136-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:124:13-36
137            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
137-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:125:13-79
138            <intent-filter>
138-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:102:13-104:29
139                <action android:name="android.accessibilityservice.AccessibilityService" />
139-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:103:17-92
139-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:103:25-89
140            </intent-filter>
141
142            <meta-data
142-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:105:13-107:78
143                android:name="android.accessibilityservice"
143-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:106:17-60
144                android:resource="@xml/outlook_accessibility_service_config" />
144-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:107:17-75
145        </service>
146
147        <!-- FileProvider for sharing PDF files -->
148        <provider
149            android:name="androidx.core.content.FileProvider"
149-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:137:13-62
150            android:authorities="com.tech.ekvayu.fileprovider"
150-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:138:13-64
151            android:exported="false"
151-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:139:13-37
152            android:grantUriPermissions="true" >
152-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:140:13-47
153            <meta-data
153-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:141:13-143:54
154                android:name="android.support.FILE_PROVIDER_PATHS"
154-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:142:17-67
155                android:resource="@xml/file_paths" />
155-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:143:17-51
156        </provider>
157
158        <!-- Google Play Services Auth metadata -->
159        <meta-data
159-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:147:9-149:69
160            android:name="com.google.android.gms.version"
160-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:148:13-58
161            android:value="@integer/google_play_services_version" />
161-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:149:13-66
162
163        <provider
163-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
164            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
164-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
165            android:authorities="com.tech.ekvayu.mlkitinitprovider"
165-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
166            android:exported="false"
166-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
167            android:initOrder="99" />
167-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
168
169        <service
169-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
170            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
170-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:16:13-91
171            android:directBootAware="true"
171-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
172            android:exported="false" >
172-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:18:13-37
173            <meta-data
173-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
174                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
174-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
176        </service>
177
178        <activity
178-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
179            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
179-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
180            android:excludeFromRecents="true"
180-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
181            android:exported="true"
181-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
182            android:launchMode="singleTask"
182-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
183            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
183-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
184            <intent-filter>
184-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
185                <action android:name="android.intent.action.VIEW" />
185-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
185-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
186
187                <category android:name="android.intent.category.DEFAULT" />
187-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
187-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
188                <category android:name="android.intent.category.BROWSABLE" />
188-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:89:17-78
188-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:89:27-75
189
190                <data
190-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
191                    android:host="firebase.auth"
191-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:92:21-50
192                    android:path="/"
193                    android:scheme="genericidp" />
193-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:93:21-53
194            </intent-filter>
195        </activity>
196        <activity
196-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
197            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
197-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
198            android:excludeFromRecents="true"
198-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
199            android:exported="true"
199-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
200            android:launchMode="singleTask"
200-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
201            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
201-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
202            <intent-filter>
202-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
203                <action android:name="android.intent.action.VIEW" />
203-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:17-69
203-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:53:25-66
204
205                <category android:name="android.intent.category.DEFAULT" />
205-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-76
205-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-73
206                <category android:name="android.intent.category.BROWSABLE" />
206-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:89:17-78
206-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:89:27-75
207
208                <data
208-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:55:17-60
209                    android:host="firebase.auth"
209-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:92:21-50
210                    android:path="/"
211                    android:scheme="recaptcha" />
211-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:93:21-53
212            </intent-filter>
213        </activity>
214
215        <service
215-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
216            android:name="com.google.firebase.components.ComponentDiscoveryService"
216-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
217            android:directBootAware="true"
217-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
218            android:exported="false" >
218-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
219            <meta-data
219-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
220                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
220-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
222            <meta-data
222-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
223                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
223-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
224                android:value="com.google.firebase.components.ComponentRegistrar" />
224-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
225            <meta-data
225-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
226                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
226-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
228        </service>
229
230        <activity
230-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
231            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
231-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
232            android:excludeFromRecents="true"
232-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
233            android:exported="false"
233-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
234            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
234-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
235        <!--
236            Service handling Google Sign-In user revocation. For apps that do not integrate with
237            Google Sign-In, this service will never be started.
238        -->
239        <service
239-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
240            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
240-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
241            android:exported="true"
241-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
242            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
242-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
243            android:visibleToInstantApps="true" />
243-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
244
245        <activity
245-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
246            android:name="com.google.android.gms.common.api.GoogleApiActivity"
246-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
247            android:exported="false"
247-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
248            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
248-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
249
250        <provider
250-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
251            android:name="com.google.firebase.provider.FirebaseInitProvider"
251-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
252            android:authorities="com.tech.ekvayu.firebaseinitprovider"
252-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
253            android:directBootAware="true"
253-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
254            android:exported="false"
254-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
255            android:initOrder="100" />
255-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
256        <provider
256-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
257            android:name="androidx.startup.InitializationProvider"
257-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
258            android:authorities="com.tech.ekvayu.androidx-startup"
258-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
259            android:exported="false" >
259-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
260            <meta-data
260-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
261                android:name="androidx.emoji2.text.EmojiCompatInitializer"
261-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
262                android:value="androidx.startup" />
262-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
263            <meta-data
263-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
264                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
264-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
265                android:value="androidx.startup" />
265-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
266            <meta-data
266-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
267                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
267-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
268                android:value="androidx.startup" />
268-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
269        </provider>
270
271        <receiver
271-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
272            android:name="androidx.profileinstaller.ProfileInstallReceiver"
272-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
273            android:directBootAware="false"
273-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
274            android:enabled="true"
274-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
275            android:exported="true"
275-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
276            android:permission="android.permission.DUMP" >
276-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
277            <intent-filter>
277-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
278                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
278-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
278-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
279            </intent-filter>
280            <intent-filter>
280-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
281                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
281-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
281-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
282            </intent-filter>
283            <intent-filter>
283-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
284                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
284-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
284-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
285            </intent-filter>
286            <intent-filter>
286-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
287                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
287-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
287-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
288            </intent-filter>
289        </receiver>
290
291        <service
291-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
292            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
292-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
293            android:exported="false" >
293-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
294            <meta-data
294-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
295                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
295-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
296                android:value="cct" />
296-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
297        </service>
298        <service
298-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
299            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
299-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
300            android:exported="false"
300-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
301            android:permission="android.permission.BIND_JOB_SERVICE" >
301-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
302        </service>
303
304        <receiver
304-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
305            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
305-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
306            android:exported="false" />
306-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
307    </application>
308
309</manifest>
