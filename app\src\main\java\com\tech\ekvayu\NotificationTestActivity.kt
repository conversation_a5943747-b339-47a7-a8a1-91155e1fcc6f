package com.tech.ekvayu

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import java.io.File

class NotificationTestActivity : AppCompatActivity() {

    private lateinit var testButton: Button
    private lateinit var statusText: TextView
    private lateinit var logText: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(createLayout())
        
        title = "🔔 Notification Test"
        
        setupNotificationChannel()
        checkNotificationPermission()
    }

    private fun createLayout(): LinearLayout {
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // Title
        val titleText = TextView(this).apply {
            text = "🔔 Notification System Test"
            textSize = 20f
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 16)
        }

        // Status
        statusText = TextView(this).apply {
            text = "Ready to test notifications..."
            textSize = 14f
            setPadding(0, 0, 0, 16)
        }

        // Test button
        testButton = Button(this).apply {
            text = "🧪 Test PDF Notification"
            setOnClickListener { testPdfNotification() }
        }

        // Log text (scrollable)
        val scrollView = ScrollView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
        }

        logText = TextView(this).apply {
            text = "📋 Test logs will appear here...\n"
            textSize = 12f
            setPadding(8, 8, 8, 8)
            setBackgroundColor(android.graphics.Color.parseColor("#F5F5F5"))
        }

        scrollView.addView(logText)

        mainLayout.addView(titleText)
        mainLayout.addView(statusText)
        mainLayout.addView(testButton)
        mainLayout.addView(scrollView)

        return mainLayout
    }

    private fun setupNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = "email_pdf_channel"
            val channel = NotificationChannel(
                channelId,
                "Email PDF Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for generated email PDF files"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            
            addLog("✅ Notification channel created: $channelId")
        } else {
            addLog("ℹ️ Android version < 8.0, no channel needed")
        }
    }

    private fun checkNotificationPermission() {
        val notificationManager = NotificationManagerCompat.from(this)
        val areNotificationsEnabled = notificationManager.areNotificationsEnabled()
        
        if (areNotificationsEnabled) {
            addLog("✅ Notifications are enabled")
            statusText.text = "✅ Notifications enabled - Ready to test"
        } else {
            addLog("❌ Notifications are disabled")
            statusText.text = "❌ Notifications disabled - Please enable in settings"
            
            // Show settings intent
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val intent = Intent().apply {
                    action = android.provider.Settings.ACTION_APP_NOTIFICATION_SETTINGS
                    putExtra(android.provider.Settings.EXTRA_APP_PACKAGE, packageName)
                }
                try {
                    startActivity(intent)
                } catch (e: Exception) {
                    addLog("❌ Could not open notification settings: ${e.message}")
                }
            }
        }
    }

    private fun testPdfNotification() {
        addLog("🧪 Starting notification test...")
        
        try {
            // Create a test PDF file path (doesn't need to exist for notification test)
            val testPdfPath = "/storage/emulated/0/Download/Email_Print_Test_Notification_${System.currentTimeMillis()}.pdf"
            val testFileName = "Test_Email_PDF_${System.currentTimeMillis()}.pdf"
            
            addLog("📄 Test PDF: $testFileName")
            
            // Create intent to open PDF reader
            val intent = Intent(this, PdfReaderActivity::class.java).apply {
                putExtra("PDF_PATH", testPdfPath)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }

            val pendingIntent = PendingIntent.getActivity(
                this,
                System.currentTimeMillis().toInt(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Create notification
            val channelId = "email_pdf_channel"
            val notificationBuilder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                android.app.Notification.Builder(this, channelId)
            } else {
                @Suppress("DEPRECATION")
                android.app.Notification.Builder(this)
                    .setPriority(android.app.Notification.PRIORITY_HIGH)
            }

            val notification = notificationBuilder
                .setContentTitle("📄 Test Email PDF Created")
                .setContentText("Tap to test: $testFileName")
                .setSmallIcon(android.R.drawable.ic_menu_save)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setDefaults(android.app.Notification.DEFAULT_ALL)
                .addAction(
                    android.R.drawable.ic_menu_view,
                    "Open PDF",
                    pendingIntent
                )
                .build()

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val notificationId = System.currentTimeMillis().toInt()
            
            notificationManager.notify(notificationId, notification)
            
            addLog("✅ Test notification sent (ID: $notificationId)")
            addLog("👆 Check your notification panel!")
            statusText.text = "✅ Test notification sent - Check notification panel"
            
        } catch (e: Exception) {
            addLog("❌ Test notification failed: ${e.message}")
            statusText.text = "❌ Test failed - Check logs"
            Log.e("NotificationTest", "Test failed", e)
        }
    }

    private fun addLog(message: String) {
        val timestamp = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.US)
            .format(java.util.Date())
        
        runOnUiThread {
            logText.append("[$timestamp] $message\n")
            
            // Auto-scroll to bottom
            val scrollView = logText.parent as ScrollView
            scrollView.post {
                scrollView.fullScroll(ScrollView.FOCUS_DOWN)
            }
        }
        
        Log.d("NotificationTest", message)
    }
}
