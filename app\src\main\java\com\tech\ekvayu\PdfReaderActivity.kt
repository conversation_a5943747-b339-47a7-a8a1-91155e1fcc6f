package com.tech.ekvayu

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.pdf.PdfRenderer
import android.net.Uri
import android.os.Bundle
import android.os.ParcelFileDescriptor
import android.util.Log
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import java.io.File
import java.io.IOException

class PdfReaderActivity : AppCompatActivity() {

    private lateinit var imageView: ImageView
    private lateinit var previousButton: Button
    private lateinit var nextButton: Button
    private lateinit var pageInfoText: TextView
    private lateinit var closeButton: Button
    private lateinit var shareButton: Button
    private lateinit var printButton: Button

    private var pdfRenderer: PdfRenderer? = null
    private var currentPage: PdfRenderer.Page? = null
    private var currentPageIndex = 0
    private var pageCount = 0
    private var pdfFile: File? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(createLayout())
        
        setupViews()
        
        // Get PDF file path from intent
        val pdfPath = intent.getStringExtra("PDF_PATH")
        val pdfUri = intent.getParcelableExtra<Uri>("PDF_URI")
        
        when {
            pdfPath != null -> {
                pdfFile = File(pdfPath)
                openPdfFromFile(pdfFile!!)
            }
            pdfUri != null -> {
                openPdfFromUri(pdfUri)
            }
            else -> {
                Toast.makeText(this, "No PDF file provided", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }

    private fun createLayout(): LinearLayout {
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // Top controls
        val topControls = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
        }

        closeButton = Button(this).apply {
            text = "✕ Close"
            setOnClickListener { finish() }
        }

        shareButton = Button(this).apply {
            text = "📤 Share"
            setOnClickListener { sharePdf() }
        }

        printButton = Button(this).apply {
            text = "🖨️ Print"
            setOnClickListener { printPdf() }
        }

        topControls.addView(closeButton)
        topControls.addView(shareButton)
        topControls.addView(printButton)

        // Page info
        pageInfoText = TextView(this).apply {
            text = "Loading PDF..."
            textAlignment = TextView.TEXT_ALIGNMENT_CENTER
            textSize = 16f
            setPadding(0, 16, 0, 16)
        }

        // PDF viewer
        imageView = ImageView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f // Take remaining space
            )
            scaleType = ImageView.ScaleType.FIT_CENTER
            setBackgroundColor(android.graphics.Color.WHITE)
        }

        // Bottom navigation
        val bottomControls = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
        }

        previousButton = Button(this).apply {
            text = "◀ Previous"
            setOnClickListener { showPreviousPage() }
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
        }

        nextButton = Button(this).apply {
            text = "Next ▶"
            setOnClickListener { showNextPage() }
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
        }

        bottomControls.addView(previousButton)
        bottomControls.addView(nextButton)

        // Add all views to main layout
        mainLayout.addView(topControls)
        mainLayout.addView(pageInfoText)
        mainLayout.addView(imageView)
        mainLayout.addView(bottomControls)

        return mainLayout
    }

    private fun setupViews() {
        title = "PDF Reader - Email Viewer"
        
        // Initially disable navigation buttons
        previousButton.isEnabled = false
        nextButton.isEnabled = false
    }

    private fun openPdfFromFile(file: File) {
        try {
            if (!file.exists()) {
                Toast.makeText(this, "PDF file not found", Toast.LENGTH_SHORT).show()
                finish()
                return
            }

            val fileDescriptor = ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY)
            pdfRenderer = PdfRenderer(fileDescriptor)
            pageCount = pdfRenderer!!.pageCount
            
            Log.d("PDFReader", "📄 PDF opened: ${file.name}, Pages: $pageCount")
            
            updatePageInfo()
            showPage(0)
            
        } catch (e: IOException) {
            Log.e("PDFReader", "❌ Error opening PDF: ${e.message}")
            Toast.makeText(this, "Error opening PDF: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    private fun openPdfFromUri(uri: Uri) {
        try {
            val fileDescriptor = contentResolver.openFileDescriptor(uri, "r")
            if (fileDescriptor != null) {
                pdfRenderer = PdfRenderer(fileDescriptor)
                pageCount = pdfRenderer!!.pageCount
                
                Log.d("PDFReader", "📄 PDF opened from URI, Pages: $pageCount")
                
                updatePageInfo()
                showPage(0)
            }
        } catch (e: IOException) {
            Log.e("PDFReader", "❌ Error opening PDF from URI: ${e.message}")
            Toast.makeText(this, "Error opening PDF: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    private fun showPage(index: Int) {
        if (pdfRenderer == null) return
        
        try {
            // Close previous page
            currentPage?.close()
            
            // Open new page
            currentPage = pdfRenderer!!.openPage(index)
            currentPageIndex = index
            
            // Create bitmap for the page
            val bitmap = Bitmap.createBitmap(
                currentPage!!.width * 2, // 2x resolution for better quality
                currentPage!!.height * 2,
                Bitmap.Config.ARGB_8888
            )
            
            // Render page to bitmap
            currentPage!!.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
            
            // Display bitmap
            imageView.setImageBitmap(bitmap)
            
            // Update navigation buttons
            previousButton.isEnabled = index > 0
            nextButton.isEnabled = index < pageCount - 1
            
            updatePageInfo()
            
            Log.d("PDFReader", "📄 Showing page ${index + 1} of $pageCount")
            
        } catch (e: Exception) {
            Log.e("PDFReader", "❌ Error showing page: ${e.message}")
            Toast.makeText(this, "Error displaying page", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showPreviousPage() {
        if (currentPageIndex > 0) {
            showPage(currentPageIndex - 1)
        }
    }

    private fun showNextPage() {
        if (currentPageIndex < pageCount - 1) {
            showPage(currentPageIndex + 1)
        }
    }

    private fun updatePageInfo() {
        pageInfoText.text = "Page ${currentPageIndex + 1} of $pageCount"
    }

    private fun sharePdf() {
        pdfFile?.let { file ->
            try {
                val uri = androidx.core.content.FileProvider.getUriForFile(
                    this,
                    "${packageName}.fileprovider",
                    file
                )
                
                val shareIntent = Intent(Intent.ACTION_SEND).apply {
                    type = "application/pdf"
                    putExtra(Intent.EXTRA_STREAM, uri)
                    putExtra(Intent.EXTRA_SUBJECT, "Email PDF - ${file.nameWithoutExtension}")
                    putExtra(Intent.EXTRA_TEXT, "Sharing email PDF generated by Ekvayu")
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                
                startActivity(Intent.createChooser(shareIntent, "Share PDF"))
                
            } catch (e: Exception) {
                Log.e("PDFReader", "❌ Error sharing PDF: ${e.message}")
                Toast.makeText(this, "Error sharing PDF", Toast.LENGTH_SHORT).show()
            }
        } ?: run {
            Toast.makeText(this, "No PDF file to share", Toast.LENGTH_SHORT).show()
        }
    }

    private fun printPdf() {
        pdfFile?.let { file ->
            try {
                val printManager = getSystemService(PRINT_SERVICE) as android.print.PrintManager

                // Create a custom print adapter for PDF
                val printAdapter = object : android.print.PrintDocumentAdapter() {
                    override fun onLayout(
                        oldAttributes: android.print.PrintAttributes?,
                        newAttributes: android.print.PrintAttributes,
                        cancellationSignal: android.os.CancellationSignal?,
                        callback: LayoutResultCallback,
                        extras: android.os.Bundle?
                    ) {
                        if (cancellationSignal?.isCanceled == true) {
                            callback.onLayoutCancelled()
                            return
                        }

                        val info = android.print.PrintDocumentInfo.Builder(file.nameWithoutExtension)
                            .setContentType(android.print.PrintDocumentInfo.CONTENT_TYPE_DOCUMENT)
                            .setPageCount(pageCount)
                            .build()

                        callback.onLayoutFinished(info, oldAttributes != newAttributes)
                    }

                    override fun onWrite(
                        pages: Array<out android.print.PageRange>,
                        destination: android.os.ParcelFileDescriptor,
                        cancellationSignal: android.os.CancellationSignal?,
                        callback: WriteResultCallback
                    ) {
                        try {
                            if (cancellationSignal?.isCanceled == true) {
                                callback.onWriteCancelled()
                                return
                            }

                            // Copy the PDF file to the destination
                            val input = java.io.FileInputStream(file)
                            val output = java.io.FileOutputStream(destination.fileDescriptor)

                            input.copyTo(output)

                            input.close()
                            output.close()

                            callback.onWriteFinished(arrayOf(android.print.PageRange.ALL_PAGES))

                        } catch (e: Exception) {
                            callback.onWriteFailed(e.message)
                        }
                    }
                }

                val jobName = "Email PDF - ${file.nameWithoutExtension}"
                printManager.print(jobName, printAdapter, null)

            } catch (e: Exception) {
                Log.e("PDFReader", "❌ Error printing PDF: ${e.message}")
                Toast.makeText(this, "Error printing PDF", Toast.LENGTH_SHORT).show()
            }
        } ?: run {
            Toast.makeText(this, "No PDF file to print", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            currentPage?.close()
            pdfRenderer?.close()
        } catch (e: Exception) {
            Log.e("PDFReader", "❌ Error closing PDF: ${e.message}")
        }
    }
}
