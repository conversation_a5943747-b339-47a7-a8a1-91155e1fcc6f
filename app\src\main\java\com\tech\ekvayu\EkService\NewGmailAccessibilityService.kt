package com.tech.ekvayu.EkService


import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.EmailDetectionHelper
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.databinding.LayoutProgressBinding
import com.tech.ekvayu.databinding.LayoutValidationBinding
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import kotlin.String


class NewGmailAccessibilityService : AccessibilityService() {

    private var currentPopupView: View? = null
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        if (event.packageName == "com.google.android.gm") {
            if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
                Log.d("GmailClick", "📧 Gmail click detected - extracting email details...")

                // Simple approach: Extract immediately after click with reasonable delay
                Handler(Looper.getMainLooper()).postDelayed({
                    try {
                        val root = rootInActiveWindow
                        if (root != null) {
                            // First, dump all UI elements for debugging
                            Log.d("UIDebug", "🔍 DUMPING ALL UI ELEMENTS:")
                            dumpAllUIElements(root, 0)

                            // Then extract email details
                            val emailData = extractEmailDetails(root)
                            Log.d("EmailExtraction", "📊 Extraction result: fromEmail=${emailData.fromEmail}, subject=${emailData.subject}, toEmails=${emailData.toEmails.size}")
                        }
                    } catch (e: Exception) {
                        Log.e("EmailExtraction", "❌ Error extracting email: ${e.message}")
                    }
                }, 1000) // 1 second delay to allow Gmail UI to load
            }
        }
    }



    // Data class to hold email extraction results
    data class EmailData(
        val senderName: String?,
        val fromEmail: String,
        val toEmails: Set<String>,
        val ccEmails: Set<String>,
        val bccEmails: Set<String>,
        val subject: String,
        val date: String,
        val body: String,
        val attachments: String
    ) {
        fun hasBasicInfo(): Boolean {
            return fromEmail != "Unknown Sender" || subject != "No Subject"
        }
    }

    private fun extractEmailDetails(rootNode: AccessibilityNodeInfo): EmailData {
        var senderName: String? = null
        var fromEmail: String? = null

        var subject: String? = null
        var date: String? = null
        var attachments: String? = null
        val attachmentSet = mutableSetOf<String>() // 📎 Use Set to avoid duplicates
        val emailBodyBuilder = StringBuilder()

        val toEmails = mutableSetOf<String>() // 🔁 Use Set to avoid duplicates
        val ccEmails = mutableSetOf<String>()
        val bccEmails = mutableSetOf<String>()

        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        Log.d("EmailExtraction", "🔍 Found ${possibleNodes.size} nodes to analyze")

        for (node in possibleNodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            val actualText = text ?: contentDesc ?: continue

            // Enhanced Debug
            Log.d("NodeScan", "📝 Text: '$text' | ViewId: $viewId | Desc: '$contentDesc' | Class: ${node.className}")

            // Subject - Multiple patterns
            if (subject == null) {
                if (viewId?.contains("subject", true) == true ||
                    actualText.startsWith("Subject", true) ||
                    actualText.startsWith("Re:", true) ||
                    actualText.startsWith("Fwd:", true)) {
                    subject = actualText.removePrefix("Subject:").trim()
                    Log.d("EmailExtraction", "📋 Found subject: $subject")
                } else if (actualText.length > 10 && actualText.length < 200 &&
                          !actualText.contains("@") &&
                          !actualText.contains("To:") &&
                          !actualText.contains("From:") &&
                          actualText.matches(Regex("^[A-Za-z0-9\\s\\-_.,!?()]+$"))) {
                    // Potential subject line
                    subject = actualText
                    Log.d("EmailExtraction", "📋 Potential subject: $subject")
                }
            }

            // From (Name + Email) - Multiple patterns
            if (fromEmail == null) {
                if (actualText.contains("•")) {
                    val parts = actualText.split("•").map { it.trim() }
                    if (parts.size == 2) {
                        senderName = parts[0]
                        fromEmail = parts[1].extractEmail()
                        Log.d("EmailExtraction", "✉️ Found sender (•): $senderName <$fromEmail>")
                    }
                } else if (actualText.contains("<") && actualText.contains(">")) {
                    // Format: "Name <<EMAIL>>"
                    val emailMatch = Regex("<([^>]+)>").find(actualText)
                    if (emailMatch != null) {
                        fromEmail = emailMatch.groupValues[1]
                        senderName = actualText.replace(emailMatch.value, "").trim()
                        Log.d("EmailExtraction", "✉️ Found sender (<>): $senderName <$fromEmail>")
                    }
                } else if (actualText.isValidEmail()) {
                    fromEmail = actualText
                    Log.d("EmailExtraction", "✉️ Found sender email: $fromEmail")
                }
            }

            // 🔁 Handle "To: me" or "To: yourself" - Enhanced patterns
            if (actualText.startsWith("To:", true) || actualText.startsWith("to ", true)) {
                val extracted = actualText.extractEmails()
                if (extracted.isNotEmpty()) {
                    toEmails.addAll(extracted)
                    Log.d("EmailExtraction", "📨 Found TO emails: ${extracted.joinToString(", ")}")
                } else if (actualText.contains("me", true) || actualText.contains("yourself", true)) {
                    // Fallback: Assume sending to self, so use fromEmail
                    if (fromEmail != null && fromEmail.isValidEmail()) {
                        toEmails.add(fromEmail)
                        Log.d("EmailExtraction", "📨 Found TO (self): $fromEmail")
                    }
                }
            }

            // 🔁 Extra fallback when "To:" is hidden
            if (viewId?.contains("to", true) == true && actualText.contains("@")) {
                val extracted = actualText.extractEmails()
                toEmails.addAll(extracted)
                Log.d("EmailExtraction", "📨 Found TO (viewId): ${extracted.joinToString(", ")}")
            }

            // General email detection - but be more selective
            if (actualText.isValidEmail() &&
                !toEmails.contains(actualText) &&
                fromEmail != actualText) { // Don't add sender as recipient
                toEmails.add(actualText)
                Log.d("EmailExtraction", "📨 Found general email: $actualText")
            }

            // Cc & Bcc
            if (actualText.startsWith("Cc:", true)) {
                ccEmails.addAll(actualText.extractEmails())
            }

            if (actualText.startsWith("Bcc:", true)) {
                bccEmails.addAll(actualText.extractEmails())
            }

            // Date - Enhanced patterns
            if (date == null) {
                if (actualText.contains("AM") || actualText.contains("PM") ||
                    actualText.matches(Regex("\\d{1,2}:\\d{2}")) ||
                    actualText.matches(Regex("\\d{1,2}/\\d{1,2}/\\d{2,4}")) ||
                    actualText.matches(Regex("\\d{1,2}-\\d{1,2}-\\d{2,4}")) ||
                    actualText.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*")) ||
                    actualText.matches(Regex("\\w{3}\\s+\\d{1,2}")) || // "Jan 15"
                    actualText.contains("today", true) ||
                    actualText.contains("yesterday", true)) {
                    date = actualText
                    Log.d("EmailExtraction", "📅 Found date: $date")
                }
            }

            // Body
            if (actualText.length > 50 &&
                !actualText.contains("@") &&
                !actualText.startsWith("To", true) &&
                !actualText.startsWith("Cc", true) &&
                !actualText.startsWith("Bcc", true)
            ) {
                emailBodyBuilder.appendLine(actualText)
            }

            // 📎 Simple Attachment Detection (only for current email)
            if (attachments == null) {
                val detectedAttachment = detectSingleAttachment(actualText, viewId, contentDesc)
                if (detectedAttachment != null) {
                    attachmentSet.add(detectedAttachment)
                }
            }

        }

        // 🔍 FALLBACK: Try to extract missing data from body content
        val bodyContent = if (emailBodyBuilder.isNotEmpty()) emailBodyBuilder.toString().trim() else ""
        if (bodyContent.isNotEmpty()) {
            Log.d("EmailExtraction", "🔍 Attempting fallback extraction from body content (${bodyContent.length} chars)")
            Log.d("EmailExtraction", "🔍 Body preview: ${bodyContent.take(200)}...")

            // Try to find sender in body
            if (fromEmail == null) {
                val fromPattern = Regex("From:\\s*([^\\n]+)", RegexOption.IGNORE_CASE)
                val fromMatch = fromPattern.find(bodyContent)
                if (fromMatch != null) {
                    val fromLine = fromMatch.groupValues[1].trim()
                    if (fromLine.contains("<") && fromLine.contains(">")) {
                        val emailMatch = Regex("<([^>]+)>").find(fromLine)
                        if (emailMatch != null) {
                            fromEmail = emailMatch.groupValues[1]
                            senderName = fromLine.replace(emailMatch.value, "").trim()
                            Log.d("EmailExtraction", "🔍 Fallback found sender: $senderName <$fromEmail>")
                        }
                    } else if (fromLine.isValidEmail()) {
                        fromEmail = fromLine
                        Log.d("EmailExtraction", "🔍 Fallback found sender email: $fromEmail")
                    }
                }
            }

            // Try to find subject in body
            if (subject == null) {
                val subjectPattern = Regex("Subject:\\s*([^\\n]+)", RegexOption.IGNORE_CASE)
                val subjectMatch = subjectPattern.find(bodyContent)
                if (subjectMatch != null) {
                    subject = subjectMatch.groupValues[1].trim()
                    Log.d("EmailExtraction", "🔍 Fallback found subject: $subject")
                }
            }

            // Try to find recipients in body
            if (toEmails.isEmpty()) {
                val toPattern = Regex("To:\\s*([^\\n]+)", RegexOption.IGNORE_CASE)
                val toMatch = toPattern.find(bodyContent)
                if (toMatch != null) {
                    val toLine = toMatch.groupValues[1].trim()
                    val extractedEmails = toLine.extractEmails()
                    if (extractedEmails.isNotEmpty()) {
                        toEmails.addAll(extractedEmails)
                        Log.d("EmailExtraction", "🔍 Fallback found TO: ${extractedEmails.joinToString(", ")}")
                    }
                }
            }
        }

        // Fallbacks
        subject = subject ?: "No Subject"
        fromEmail = fromEmail ?: "Unknown Sender"
        date = date ?: "Unknown Date"
        val body = if (bodyContent.isNotEmpty()) bodyContent else "No Body Content"

        // 📎 Finalize attachment information (only unique attachments)
        attachments = if (attachmentSet.isNotEmpty()) {
            "Attachments (${attachmentSet.size}): ${attachmentSet.joinToString(", ")}"
        } else {
            "No Attachments"
        }



        Log.d("AttachmentExtraction", "📎 Final attachment summary: $attachments")
        Log.d("AttachmentExtraction", "📎 Unique attachments found: ${attachmentSet.joinToString(" | ")}")
        Log.d("AttachmentExtraction", "📎 Total attachment count: ${attachmentSet.size}")

        // Create EmailData object
        val emailData = EmailData(
            senderName = senderName,
            fromEmail = fromEmail ?: "Unknown Sender",
            toEmails = toEmails,
            ccEmails = ccEmails,
            bccEmails = bccEmails,
            subject = subject ?: "No Subject",
            date = date ?: "No Date",
            body = body,
            attachments = attachments ?: "No Attachments"
        )

        // Comprehensive extraction summary
        Log.d("EmailSummary", """
        📧 EMAIL EXTRACTION SUMMARY:
        ✉️ From: ${emailData.fromEmail} (${emailData.senderName})
        📨 To: ${emailData.toEmails.joinToString(", ")} (${emailData.toEmails.size} recipients)
        📋 Subject: ${emailData.subject}
        📅 Date: ${emailData.date}
        📄 Body Length: ${emailData.body.length} chars
        📎 Attachments: ${emailData.attachments}
        🔍 Has Basic Info: ${emailData.hasBasicInfo()}
        """.trimIndent())

        // Process the email if we have any meaningful data
        if (emailData.hasBasicInfo()) {
            Log.d("EmailExtraction", "✅ Processing email with available data")
            processCompleteEmail(emailData)
        } else {
            Log.d("EmailExtraction", "⚠️ No meaningful email data found: fromEmail=${emailData.fromEmail}, subject=${emailData.subject}")
        }

        return emailData
    }

    private fun processCompleteEmail(emailData: EmailData) {
        Log.d("EmailDetails", """
        ✅ COMPLETE EMAIL EXTRACTED:
        senderName: ${emailData.senderName}
        fromEmail: ${emailData.fromEmail}
        toEmail: ${emailData.toEmails.joinToString(", ")}
        cc: ${emailData.ccEmails.joinToString(", ")}
        bcc: ${emailData.bccEmails.joinToString(", ")}
        subject: ${emailData.subject}
        date: ${emailData.date}
        body: ${emailData.body}
        attachments: ${emailData.attachments}
    """.trimIndent())

        // Create EML file
        val emailContent = """
        From: ${emailData.fromEmail}
        To: ${emailData.toEmails.joinToString(", ")}
        Cc: ${emailData.ccEmails.joinToString(", ")}
        Bcc: ${emailData.bccEmails.joinToString(", ")}
        Subject: ${emailData.subject}
        Date: ${emailData.date}
        MIME-Version: 1.0
        Content-Type: text/plain; charset=UTF-8

        ${emailData.body}
    """.trimIndent()

        val file = File(applicationContext.filesDir, "email.eml")
        try {
            FileOutputStream(file).use { it.write(emailContent.toByteArray()) }
            readEmlFile(file.absolutePath)?.let {
                Log.d("getEmailContent", "saveEmailAsEml: $it")
            }
        } catch (e: IOException) {
            Log.e("FileWriteError", "Failed to write eml file", e)
        }

        // Fully automatic receiver email detection
        val automaticReceiverEmail = EmailDetectionHelper.determineReceiverEmail(
            fromEmail = emailData.fromEmail,
            toEmails = emailData.toEmails.toList(),
            ccEmails = emailData.ccEmails.toList(),
            bccEmails = emailData.bccEmails.toList(),
            context = applicationContext
        )

        Log.d("EmailProcessing", "🤖 AUTOMATIC receiver email determined: $automaticReceiverEmail")

        // Store the determined receiver email
        sharedPrefManager.putString(AppConstant.receiverMail, automaticReceiverEmail)


        Log.d("getAllDatas", "file: "+file+ " automaticReceiverEmail "+automaticReceiverEmail+" fromEmail "+emailData.fromEmail+" ccEmails "+emailData.ccEmails.toList()+
                " bccEmails "+emailData.bccEmails.toList()+" subject "+emailData.subject+" date "+emailData.date+" body "+emailData.body+" attachments "+emailData.attachments)
       /* getHashId(
            file,
            automaticReceiverEmail,
            emailData.fromEmail,
            emailData.ccEmails.toList(),
            emailData.bccEmails.toList(),
            emailData.subject,
            emailData.date,
            emailData.body,
            emailData.attachments
        )*/
    }

    fun String.extractEmails(): List<String> {
        return Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}").findAll(this).map { it.value }.toList()
    }

    fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }

    fun String.extractEmail(): String? = this.extractEmails().firstOrNull()

    private fun detectSingleAttachment(text: String, viewId: String?, contentDesc: String?): String? {
        // 📎 Priority 1: Direct file name with extension
        val fileExtensionPattern = Regex(
            """[\w\-\s()]+\.(pdf|docx?|xlsx?|pptx?|csv|txt|zip|rar|7z|tar|gz|jpg|jpeg|png|gif|bmp|svg|mp4|avi|mov|mp3|wav|flac|ppt|xls|doc|rtf)""",
            RegexOption.IGNORE_CASE
        )

        val fileMatch = fileExtensionPattern.find(text)
        if (fileMatch != null) {
            val fileName = fileMatch.value.trim()
            Log.d("AttachmentDetection", "📎 Found file: $fileName")
            return fileName
        }

        // 📎 Priority 2: Attachment count (e.g., "1 attachment", "3 files")
        if (text.matches(Regex("\\d+\\s+(attachment|file)s?", RegexOption.IGNORE_CASE))) {
            Log.d("AttachmentDetection", "📎 Found attachment count: $text")
            return text.trim()
        }

        // 📎 Priority 3: Generic attachment keywords (only if specific)
        if (text.contains("attachment", true) && text.length < 50) {
            Log.d("AttachmentDetection", "📎 Found attachment indicator: $text")
            return text.trim()
        }

        return null
    }

    private fun getHashId(emlContent:File,email: String, fromEmail: String, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String, attachments: String) {

        if (hasOverlayPermission(applicationContext)) {

            val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
            val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

            val tomail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), email.toString())
            val senderEmail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), fromEmail.toString())

            val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
            val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
            val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)

            val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
            val retrofit = ApiClient.getRetrofitInstance(applicationContext)
            val apiService = retrofit!!.create(ApiService::class.java)
            val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
            val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
            val binding = LayoutProgressBinding.inflate(layoutInflater)
            val view = binding.root
            view.setBackgroundResource(R.drawable.bg_alert_dialog)
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                android.graphics.PixelFormat.TRANSLUCENT
            )
            params.dimAmount = 0.5f
            windowManager.addView(view, params)


            apiService.getHashKey(tomail,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
                .enqueue(object : Callback<HashResponse> {
                    override fun onResponse(
                        call: Call<HashResponse>,
                        response: Response<HashResponse>
                    ) {
                        windowManager.removeView(view)

                        if (response.body()!!.Code.toString()=="1")
                        {
                            windowManager.removeView(view)
                            sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                            sharedPrefManager.putString(AppConstant.receiverMail,email)
                            // Mark mail as configured when we successfully process an email
                            sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                            checkAiResponse(email,response.body()?.hashId.toString())
                        }

                        else
                        {
                            windowManager.removeView(view)
                            uploadFile(email,fromEmail,emlContent,cc,bcc,subject,date,body)
                        }
                    }

                    override fun onFailure(call: Call<HashResponse>, t: Throwable) {
                        windowManager.removeView(view)
                    }
                })
        }

        else
        {
            requestOverlayPermission(applicationContext)
        }
    }

    private fun uploadFile(email: String, fromEmail: String, emlContent: File, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String) {

        val emailRequestBody = RequestBody.create("text/plain".toMediaTypeOrNull(), email)
        val senderEmail = RequestBody.create("text/plain".toMediaTypeOrNull(), fromEmail.toString())
        val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
        val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

        val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
        val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
        val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)


        val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)


        apiService.uploadFile(emailRequestBody,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(
                    call: Call<EmailResponse>,
                    response: Response<EmailResponse>
                ) {

                    if (response.body()!!.Code==1)
                    {
                        sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                        sharedPrefManager.putString(AppConstant.receiverMail,email)
                        // Mark mail as configured when we successfully process an email
                        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                        //  setResponsePopup(response.body()?.unsafeReasons,response.body()?.emailStatus.toString(),email,response.body()?.hashId.toString())
                        checkAiResponse(email,response.body()?.hashId.toString())
                    }
                    else
                    {
                        Toast.makeText(applicationContext, ""+response.body()!!.message, Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<EmailResponse?>, t: Throwable) {

                }

            })
    }

    private fun checkAiResponse(email: String, hashId: String) {
        // Get system services
        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // Inflate loading layout
        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
       // view.setBackgroundResource(R.drawable.bg_alert_dialog)

        // Define layout params
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        // Show loading overlay
        windowManager.addView(view, params)

        val handler = Handler(Looper.getMainLooper())
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        var retryCount = 0
        val maxRetries = 10  // Optional: limit max retries to avoid infinite loop

        // Polling function
        fun pollStatus() {
            if (retryCount >= maxRetries) {
                windowManager.removeView(view)
                Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                return
            }

            val request = PendingMailRequest(email = email, hashId = hashId)

            apiService.pendingMailStatusAi(request)
                .enqueue(object : Callback<PedingMailRes> {
                    override fun onResponse(call: Call<PedingMailRes>, response: Response<PedingMailRes>) {
                        val data = response.body()?.data
                        val status = data?.emlStatus?.lowercase()?.trim()

                        Log.d("checkAiResponse", "Polling attempt $retryCount: status=$status")

                        if (status == "safe" || status == "unsafe") {
                            windowManager.removeView(view)
                            setResponsePopup(
                                unsafeReasons = data.unsafeReasons.toString(),
                                mailStatus = status,
                                email = email,
                                hashId = hashId
                            )
                        }
                        else if (retryCount==10 && status == "pending")
                        {
                            windowManager.removeView(view)
                            Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                        }

                        else {
                            retryCount++
                            handler.postDelayed({ pollStatus() }, 3000) // Retry after 3 seconds
                        }
                    }

                    override fun onFailure(call: Call<PedingMailRes>, t: Throwable) {
                        Log.e("checkAiResponse", "API call failed: ${t.message}")
                        retryCount++
                        handler.postDelayed({ pollStatus() }, 3000)
                    }
                })
        }

        // Start polling
        pollStatus()
    }

    private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {

        if (!hasOverlayPermission(applicationContext)) {
            requestOverlayPermission(applicationContext)
            return
        }

        val windowManager = applicationContext.getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = applicationContext.getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val binding = LayoutValidationBinding.inflate(layoutInflater)


        currentPopupView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        // Configure popup UI
        when (mailStatus) {
            "safe" -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
            }
            "pending" -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.btClose.visibility = View.VISIBLE
            }
            "unsafe" -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
            }
        }

        val view = binding.root
        view.setBackgroundResource(R.drawable.bg_alert_dialog)

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        binding.tvMessage.text = unsafeReasons
        binding.btClose.setOnClickListener {
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        windowManager.addView(view, params)
        currentPopupView = view // Track the current popup
    }

    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    private fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$context.packageName")
                )
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    private fun readEmlFile(filePath: String): String? {
        val file = File(filePath)
        if (!file.exists()) {
            return null
        }
        val stringBuilder = StringBuilder()
        try {
            val reader = BufferedReader(FileReader(file))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append(line).append("\n")
            }
            reader.close()
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
        return stringBuilder.toString()
    }

    private fun findEmailNodes(node: AccessibilityNodeInfo?, emailNodes: MutableList<AccessibilityNodeInfo>) {
        if (node == null) return

        // Include more node types for better extraction
        if ((node.className == "android.widget.TextView" ||
             node.className == "android.widget.EditText" ||
             node.className == "android.view.View") &&
            (node.text != null || node.contentDescription != null)) {
            emailNodes.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findEmailNodes(child, emailNodes)
            }
        }
    }

    private fun dumpAllUIElements(node: AccessibilityNodeInfo?, depth: Int) {
        if (node == null || depth > 5) return // Limit depth to avoid too much output

        val indent = "  ".repeat(depth)
        val text = node.text?.toString()?.take(50) // Limit text length
        val contentDesc = node.contentDescription?.toString()?.take(50)
        val viewId = node.viewIdResourceName
        val className = node.className

        if (text != null || contentDesc != null || viewId != null) {
            Log.d("UIDebug", "${indent}📱 Class: $className | Text: '$text' | Desc: '$contentDesc' | ViewId: $viewId | Clickable: ${node.isClickable}")
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                dumpAllUIElements(child, depth + 1)
            }
        }
    }

    override fun onInterrupt() {

    }




}
