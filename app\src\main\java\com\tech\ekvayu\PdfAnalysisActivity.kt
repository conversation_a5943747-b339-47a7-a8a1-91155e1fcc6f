package com.tech.ekvayu

import android.content.Intent
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import java.io.File

class PdfAnalysisActivity : AppCompatActivity() {

    private lateinit var scrollView: ScrollView
    private lateinit var contentLayout: LinearLayout
    private lateinit var analyzeButton: Button
    private lateinit var statusText: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(createLayout())
        
        title = "📄 PDF Content Analyzer"
        
        // Check if specific PDF path was provided
        val pdfPath = intent.getStringExtra("PDF_PATH")
        if (!pdfPath.isNullOrEmpty()) {
            analyzePdf(pdfPath)
        } else {
            // Analyze the specific PDF file mentioned by user
            val specificPdf = "/storage/emulated/0/Download/Email_Print_Fwd_Newsletter_Cloudinary_Developer_Bytes_Inbox_20250704_150513.pdf"
            analyzePdf(specificPdf)
        }
    }

    private fun createLayout(): LinearLayout {
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // Title
        val titleText = TextView(this).apply {
            text = "📄 PDF Content Analysis"
            textSize = 20f
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 16)
        }

        // Status
        statusText = TextView(this).apply {
            text = "Analyzing PDF content..."
            textSize = 14f
            setPadding(0, 0, 0, 8)
        }

        // Analyze button
        analyzeButton = Button(this).apply {
            text = "🔍 Re-analyze PDF"
            setOnClickListener { 
                val specificPdf = "/storage/emulated/0/Download/Email_Print_Fwd_Newsletter_Cloudinary_Developer_Bytes_Inbox_20250704_150513.pdf"
                analyzePdf(specificPdf)
            }
        }

        // Scroll view for content
        scrollView = ScrollView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f // Take remaining space
            )
        }

        // Content layout inside scroll view
        contentLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(8, 8, 8, 8)
        }

        scrollView.addView(contentLayout)

        mainLayout.addView(titleText)
        mainLayout.addView(statusText)
        mainLayout.addView(analyzeButton)
        mainLayout.addView(scrollView)

        return mainLayout
    }

    private fun analyzePdf(pdfPath: String) {
        try {
            statusText.text = "🔍 Analyzing: ${File(pdfPath).name}"
            contentLayout.removeAllViews()
            
            Log.d("PDFAnalysis", "📄 Starting analysis of: $pdfPath")
            
            val result = PdfContentAnalyzer.analyzePdfContent(this, pdfPath)
            
            if (result.error != null) {
                displayError(result.error!!)
                return
            }
            
            displayAnalysisResult(result)
            statusText.text = "✅ Analysis completed"
            
        } catch (e: Exception) {
            Log.e("PDFAnalysis", "❌ Error during analysis: ${e.message}", e)
            displayError("Analysis failed: ${e.message}")
        }
    }

    private fun displayAnalysisResult(result: PdfAnalysisResult) {
        // File Information Section
        addSectionHeader("📁 File Information")
        addInfoRow("File Name", result.fileName)
        addInfoRow("File Size", result.fileSize)
        addInfoRow("Last Modified", result.lastModified)
        addInfoRow("Page Count", result.pageCount.toString())
        addInfoRow("Page Size", "${result.pageWidth} x ${result.pageHeight} pixels")
        
        addDivider()
        
        // Email Data Section
        result.emailData?.let { emailData ->
            addSectionHeader("📧 Email Content")
            
            emailData.from?.let { addInfoRow("From", it) }
            emailData.to?.let { addInfoRow("To", it) }
            emailData.cc?.let { addInfoRow("CC", it) }
            emailData.bcc?.let { addInfoRow("BCC", it) }
            emailData.subject?.let { addInfoRow("Subject", it) }
            emailData.date?.let { addInfoRow("Date", it) }
            
            addDivider()
            
            // Message Body
            emailData.messageBody?.let { body ->
                addSectionHeader("📝 Message Body")
                addLongText(body)
                addDivider()
            }
            
            // Attachments
            emailData.attachments?.let { attachments ->
                addSectionHeader("📎 Attachments")
                addLongText(attachments)
                addDivider()
            }
        }
        
        // Raw Content Section (if available)
        if (result.extractedText.isNotEmpty() && !result.extractedText.contains("extraction not available")) {
            addSectionHeader("📄 Raw Content")
            addLongText(result.extractedText.take(1000) + if (result.extractedText.length > 1000) "\n\n... (truncated)" else "")
        }
        
        // Action Buttons
        addDivider()
        addActionButtons(result.filePath)
    }

    private fun addSectionHeader(title: String) {
        val headerText = TextView(this).apply {
            text = title
            textSize = 16f
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 16, 0, 8)
            setTextColor(android.graphics.Color.parseColor("#2196F3"))
        }
        contentLayout.addView(headerText)
    }

    private fun addInfoRow(label: String, value: String) {
        val rowLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            setPadding(0, 4, 0, 4)
        }

        val labelText = TextView(this).apply {
            text = "$label: "
            textSize = 14f
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 0.3f)
        }

        val valueText = TextView(this).apply {
            text = value
            textSize = 14f
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 0.7f)
        }

        rowLayout.addView(labelText)
        rowLayout.addView(valueText)
        contentLayout.addView(rowLayout)
    }

    private fun addLongText(text: String) {
        val textView = TextView(this).apply {
            this.text = text
            textSize = 12f
            setPadding(8, 8, 8, 8)
            setBackgroundColor(android.graphics.Color.parseColor("#F5F5F5"))
        }
        contentLayout.addView(textView)
    }

    private fun addDivider() {
        val divider = View(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                2
            ).apply { setMargins(0, 16, 0, 16) }
            setBackgroundColor(android.graphics.Color.parseColor("#E0E0E0"))
        }
        contentLayout.addView(divider)
    }

    private fun addActionButtons(pdfPath: String) {
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            setPadding(0, 16, 0, 0)
        }

        val openPdfButton = Button(this).apply {
            text = "📖 Open PDF"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            setOnClickListener {
                val intent = Intent(this@PdfAnalysisActivity, PdfReaderActivity::class.java)
                intent.putExtra("PDF_PATH", pdfPath)
                startActivity(intent)
            }
        }

        buttonLayout.addView(openPdfButton)
        contentLayout.addView(buttonLayout)
    }

    private fun displayError(error: String) {
        statusText.text = "❌ Analysis failed"
        
        val errorText = TextView(this).apply {
            text = "❌ Error: $error"
            textSize = 14f
            setTextColor(android.graphics.Color.RED)
            setPadding(8, 8, 8, 8)
        }
        contentLayout.addView(errorText)
    }
}
